import { useState, useCallback } from 'react';

export const useConfirmDialog = () => {
  const [dialogState, setDialogState] = useState({
    isOpen: false,
    title: '',
    message: '',
    confirmText: '确定',
    cancelText: '取消',
    type: 'warning',
    onConfirm: null
  });

  const showConfirm = useCallback(({
    title = '确认操作',
    message,
    confirmText = '确定',
    cancelText = '取消',
    type = 'warning',
    onConfirm
  }) => {
    setDialogState({
      isOpen: true,
      title,
      message,
      confirmText,
      cancelText,
      type,
      onConfirm
    });
  }, []);

  const hideConfirm = useCallback(() => {
    setDialogState(prev => ({ ...prev, isOpen: false }));
  }, []);

  return {
    dialogState,
    showConfirm,
    hideConfirm
  };
};