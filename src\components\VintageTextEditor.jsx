import React, { useEffect, useState } from 'react';
import { Zap, Lightbulb, Clock, Book } from 'lucide-react';
import DateTimeWeather from './DateTimeWeather';
import VoiceInputButton from './VoiceInputButton';
import NewDocumentButton from './NewDocumentButton';

// 创建一个全局共享的超时管理对象
if (typeof window !== 'undefined' && !window.bubbleManager) {
  window.bubbleManager = {
    timeoutId: null,
    activeSuggestionId: null,
    clearTimeout: function () {
      if (this.timeoutId) {
        clearTimeout(this.timeoutId);
        this.timeoutId = null;
      }
    },
    setTimeout: function (callback, delay) {
      this.clearTimeout();
      this.timeoutId = setTimeout(callback, delay);
    },
    setActiveSuggestion: function (id) {
      this.activeSuggestionId = id;
    },
    getActiveSuggestion: function () {
      return this.activeSuggestionId;
    }
  };
}

const VintageTextEditor = ({
  text,
  setText,
  suggestions,
  isAnalyzing,
  onAnalyze,
  hoveredSuggestion,
  setHoveredSuggestion,
  setBubblePosition,
  setActiveBubble,
  isImmersiveMode,
  onShowAIResponse,
  rawAIResponse,
  onShowHistory,
  onShowDictionary,
  onNewDocument,
  isDarkMode
}) => {
  // 使用全局共享的超时管理对象
  const bubbleManager = window.bubbleManager;
  
  // 语音输入状态
  const [voiceTranscript, setVoiceTranscript] = useState('');

  // 添加事件监听器，处理气泡鼠标进入事件
  useEffect(() => {
    const handleBubbleMouseEnter = () => {
      // 当鼠标进入气泡时，清除任何关闭气泡的超时
      if (bubbleManager) {
        bubbleManager.clearTimeout();
      }
    };

    // 监听自定义事件
    document.addEventListener('bubbleMouseEnter', handleBubbleMouseEnter);

    // 清理函数
    return () => {
      document.removeEventListener('bubbleMouseEnter', handleBubbleMouseEnter);
      // 清除任何可能的超时
      if (bubbleManager) {
        bubbleManager.clearTimeout();
      }
    };
  }, []);

  // 处理鼠标进入下划线
  const handleMouseEnter = (e, suggestion) => {
    // 清除任何现有的超时
    if (bubbleManager) {
      bubbleManager.clearTimeout();
      bubbleManager.setActiveSuggestion(suggestion.id);
    }

    // 立即设置悬停状态和气泡
    setHoveredSuggestion(suggestion.id);
    const rect = e.target.getBoundingClientRect();
    
    // 简化定位算法 - 让气泡紧贴文字上方
    const centerX = rect.left + rect.width / 2;
    const bubbleWidth = 384; // max-w-sm 对应的像素宽度
    
    // 水平位置：以下划线为中心
    const finalLeft = Math.max(8, Math.min(centerX - bubbleWidth / 2, window.innerWidth - bubbleWidth - 8));
    
    // 垂直位置：紧贴文字上方，让气泡自己处理高度
    const finalTop = rect.top - 8; // 很小的间距，气泡会通过 transform 显示在这个位置的上方
    
    setBubblePosition({
      x: finalLeft,
      y: finalTop,
      underlineRect: rect,
      arrowOffset: centerX - finalLeft
    });
    setActiveBubble(suggestion);
  };

  // 处理鼠标离开下划线
  const handleMouseLeave = () => {
    // 设置悬停状态为null
    setHoveredSuggestion(null);

    // 设置延迟关闭气泡，给用户时间移动到气泡上
    if (bubbleManager) {
      bubbleManager.setTimeout(() => {
        setActiveBubble(null);
        bubbleManager.setActiveSuggestion(null);
      }, 800); // 增加延迟时间，给用户更多时间移动到气泡上
    }
  };

  // 处理语音输入转录变化
  const handleVoiceTranscriptChange = (transcript) => {
    setVoiceTranscript(transcript);
  };

  // 处理语音输入完成
  const handleVoiceFinalTranscript = (finalTranscript) => {
    if (finalTranscript.trim()) {
      // 将语音转录的文本添加到编辑器
      const newText = text ? `${text} ${finalTranscript}` : finalTranscript;
      setText(newText);
    }
    setVoiceTranscript('');
  };
  // 渲染带高亮的文本
  const renderHighlightedText = () => {
    if (!text || suggestions.length === 0) {
      return text;
    }

    let result = [];
    let lastIndex = 0;

    // 获取可以高亮的建议（有具体位置信息的）
    const highlightableSuggestions = suggestions.filter(s => {
      const hasPositions = s.positions && s.positions.length > 0;
      return hasPositions;
    });

    // 确保每个建议都有唯一的位置
    const uniquePositionSuggestions = [];
    const positionMap = new Map(); // 用于跟踪已处理的位置

    highlightableSuggestions.forEach(suggestion => {
      const position = suggestion.positions[0];
      const positionKey = `${position.start}-${position.end}`;

      // 如果这个位置还没有被处理过，添加到唯一建议列表
      if (!positionMap.has(positionKey)) {
        positionMap.set(positionKey, suggestion);
        uniquePositionSuggestions.push(suggestion);
      }
    });

    // 按位置排序
    const sortedSuggestions = uniquePositionSuggestions.sort((a, b) => {
      const aPos = a.positions[0].start;
      const bPos = b.positions[0].start;
      return aPos - bPos;
    });

    sortedSuggestions.forEach((suggestion, index) => {
      const position = suggestion.positions[0];
      const startIndex = position.start;
      const endIndex = position.end;

      if (startIndex >= lastIndex) {
        // 添加高亮前的普通文本
        if (startIndex > lastIndex) {
          result.push(
            <span key={`text-${index}-${lastIndex}`}>
              {text.substring(lastIndex, startIndex)}
            </span>
          );
        }

        // 处理零长度位置（如句尾添加标点）
        if (startIndex === endIndex) {
          // 对于零长度位置，我们在该位置插入一个特殊的标记
          result.push(
            <span
              key={`suggestion-${suggestion.id}`}
              className={`suggestion-highlight ${suggestion.category}-error ${hoveredSuggestion === suggestion.id ? 'hovered' : ''
                }`}
              style={{
                position: 'relative',
                borderLeft: '2px solid',
                borderColor: isDarkMode ? '#D2691E' : '#991B1B',
                paddingLeft: '2px',
                marginLeft: '-2px',
                pointerEvents: 'auto',
                color: isDarkMode ? '#E8DCC6' : '#5D4037'
              }}
              data-suggestion-id={suggestion.id}
              onMouseEnter={(e) => handleMouseEnter(e, suggestion)}
              onMouseLeave={handleMouseLeave}

            >
              ⚬
            </span>
          );
          // 对于零长度位置，lastIndex设置为当前位置
          lastIndex = startIndex;
        } else {
          // 添加高亮文本
          const highlightText = text.substring(startIndex, endIndex);
          const isHovered = hoveredSuggestion === suggestion.id;

          result.push(
            <span
              key={suggestion.id}
              className="relative inline cursor-pointer"
              style={{
                borderBottom: `2px solid ${isDarkMode ? '#D2691E' : '#991B1B'}`,
                backgroundColor: isHovered ?
                  (isDarkMode ? 'rgba(210, 105, 30, 0.2)' : '#FEF2F2') :
                  'transparent',
                borderRadius: isHovered ? '4px' : '0',
                padding: '2px 4px', // 始终保持相同的内边距
                margin: '-2px -4px', // 使用负margin抵消padding，保持整体尺寸不变
                cursor: 'pointer',
                display: 'inline-block',
                position: 'relative',
                pointerEvents: 'auto',
                color: isDarkMode ? '#E8DCC6' : '#5D4037',
                transition: 'background-color 0.2s ease' // 只对背景色应用过渡效果
              }}
              data-suggestion-id={suggestion.id}
              onMouseEnter={(e) => handleMouseEnter(e, suggestion)}
              onMouseLeave={handleMouseLeave}

            >
              {highlightText}
            </span>
          );

          lastIndex = endIndex;
        }
      }
    });

    // 添加剩余的文本
    if (lastIndex < text.length) {
      result.push(
        <span key={`text-final-${lastIndex}`}>
          {text.substring(lastIndex)}
        </span>
      );
    }

    return result;
  };

  return (
    <div className="rounded-2xl transition-colors duration-300" style={{
      backgroundColor: isDarkMode ? '#2A241D' : '#FEFCF5',
      padding: '48px',
      height: isImmersiveMode ? 'calc(100vh - 160px)' : 'auto',
      position: 'relative'
    }}>
      <div className="flex items-center justify-between" style={{ marginBottom: '40px' }}>
        <DateTimeWeather isDarkMode={isDarkMode} />
        <div className="flex items-center gap-6">

          <div className="flex items-center gap-3">
            <button
              onClick={onShowDictionary}
              className="editor-action-btn"
              title="词典查询"
            >
              <Book
                style={{
                  width: '24px',
                  height: '24px',
                  color: isDarkMode ? '#D2691E' : '#166534',
                  fill: 'none',
                  stroke: 'currentColor',
                  strokeWidth: 2,
                  pointerEvents: 'none'
                }}
              />
            </button>
            
            <button
              onClick={onShowHistory}
              className="editor-action-btn"
              title="历史记录"
            >
              <Clock
                style={{
                  width: '24px',
                  height: '24px',
                  color: isDarkMode ? '#C4B59A' : '#8B4513',
                  fill: 'none',
                  stroke: 'currentColor',
                  strokeWidth: 2,
                  pointerEvents: 'none'
                }}
              />
            </button>
          </div>

          {rawAIResponse && (
            <button
              onClick={onShowAIResponse}
              className="ai-response-btn"
              title="分析与改进建议"
            >
              <Lightbulb
                style={{
                  width: '24px',
                  height: '24px',
                  color: isDarkMode ? '#C4B59A' : '#8B4513',
                  fill: 'none',
                  stroke: 'currentColor',
                  strokeWidth: 2,
                  pointerEvents: 'none'
                }}
              />
            </button>
          )}
        </div>
      </div>

      {/* 编辑区域 */}
      <div className="relative">
        {/* 底部按钮组 */}
        <div className="absolute bottom-4 right-4 z-20 flex items-center gap-2">
          {/* 新文档按钮 */}
          <NewDocumentButton
            onNewDocument={onNewDocument}
            isDarkMode={isDarkMode}
            disabled={isAnalyzing}
          />
          
          {/* 语音输入按钮 */}
          <VoiceInputButton
            onTranscriptChange={handleVoiceTranscriptChange}
            onFinalTranscript={handleVoiceFinalTranscript}
            isDarkMode={isDarkMode}
            disabled={isAnalyzing}
          />
        </div>

        {/* 语音转录预览 */}
        {voiceTranscript && (
          <div
            className="absolute bottom-4 right-24 p-3 rounded-xl text-sm z-15"
            style={{
              backgroundColor: isDarkMode ? '#4A3F35' : '#F0E6D2',
              color: isDarkMode ? '#C4B59A' : '#8B4513',
              border: `1px solid ${isDarkMode ? '#6B5B4F' : '#E6D7B8'}`,
              fontFamily: 'Georgia, "Noto Serif SC", serif',
              fontStyle: 'italic',
              maxHeight: '100px',
              maxWidth: '300px',
              minWidth: '200px',
              overflowY: 'auto'
            }}
          >
            <div className="text-xs mb-1" style={{ color: isDarkMode ? '#D2691E' : '#166534' }}>
              正在倾听...
            </div>
            {voiceTranscript}
          </div>
        )}

        {/* 文本输入区域 */}
        <textarea
          value={voiceTranscript ? `${text}${voiceTranscript}` : text}
          onChange={(e) => {
            const newText = e.target.value;
            setText(newText);
          }}
          onKeyDown={(e) => {
            if (e.key === 'Tab') {
              e.preventDefault();
              if (!isAnalyzing && text.trim().length >= 10) {
                onAnalyze();
              }
            }
          }}
          placeholder="在这里开始写作... 我会实时为你提供语法检查、风格优化和写作建议。按 Tab 键快速分析，或点击右下角麦克风图标进行语音输入。"
          className="w-full rounded-2xl focus:outline-none resize-none relative z-5"
          style={{
            fontFamily: 'Georgia, "Noto Serif SC", "Times New Roman", serif',
            fontSize: '20px',
            lineHeight: '2',
            letterSpacing: '0.05em',
            backgroundColor: isDarkMode ? '#2A241D' : '#FFFEF7',
            color: suggestions.length > 0 ? 'transparent' : (isDarkMode ? '#E8DCC6' : '#5D4037'),
            caretColor: isDarkMode ? '#E8DCC6' : '#5D4037',
            padding: '32px',
            height: isImmersiveMode ? 'calc(100vh - 320px)' : '320px',
            transition: 'background-color 0.3s ease, color 0.3s ease'
          }}
          onFocus={(e) => {
            e.target.style.backgroundColor = isDarkMode ? '#2A241D' : '#FFFDF0';
          }}
          onBlur={(e) => {
            e.target.style.backgroundColor = isDarkMode ? '#2A241D' : '#FFFEF7';
          }}
        />

        {/* 高亮覆盖层 */}
        {suggestions.length > 0 && (
          <div
            className="absolute inset-0 leading-relaxed z-10 rounded-2xl"
            style={{
              fontFamily: 'Georgia, "Noto Serif SC", "Times New Roman", serif',
              fontSize: '20px',
              lineHeight: '2',
              letterSpacing: '0.05em',
              color: isDarkMode ? '#E8DCC6' : '#5D4037',
              whiteSpace: 'pre-wrap',
              wordWrap: 'break-word',
              padding: '32px',
              pointerEvents: 'auto'
            }}
            onClick={(e) => {
              // 如果点击的不是建议元素，则让事件穿透到编辑器
              if (!e.target.hasAttribute('data-suggestion-id')) {
                e.target.style.pointerEvents = 'none';
                const textarea = e.currentTarget.previousElementSibling;
                if (textarea) {
                  textarea.focus();
                  // 计算点击位置并设置光标
                  const rect = textarea.getBoundingClientRect();
                  const x = e.clientX - rect.left;
                  const y = e.clientY - rect.top;
                  // 简单的光标定位，可以根据需要改进
                  textarea.setSelectionRange(textarea.value.length, textarea.value.length);
                }
                setTimeout(() => {
                  e.target.style.pointerEvents = 'auto';
                }, 100);
              }
            }}
          >
            {renderHighlightedText()}
          </div>
        )}


      </div>



      {/* 右下角浮动AI分析按钮 */}
      <button
        onClick={onAnalyze}
        disabled={isAnalyzing || text.trim().length < 10}
        className="analyze-btn"
        title={isAnalyzing ? "分析中..." : "AI分析 (Tab)"}
      >
        {isAnalyzing ? (
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white" style={{ borderColor: '#FEFCF5' }}></div>
        ) : (
          <Zap
            style={{
              width: '32px',
              height: '32px',
              color: '#FEFCF5',
              fill: 'none',
              stroke: 'currentColor',
              strokeWidth: 2,
              pointerEvents: 'none'
            }}
          />
        )}
      </button>
    </div>
  );
};

export default VintageTextEditor;
