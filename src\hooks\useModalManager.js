import { useState, useCallback } from 'react';

export const useModalManager = () => {
  const [activeModal, setActiveModal] = useState(null);

  const openModal = useCallback((modalName) => {
    setActiveModal(modalName);
  }, []);

  const closeModal = useCallback(() => {
    setActiveModal(null);
  }, []);

  const isModalOpen = useCallback((modalName) => {
    return activeModal === modalName;
  }, [activeModal]);

  return {
    activeModal,
    openModal,
    closeModal,
    isModalOpen,
  };
};
