import React, { useEffect } from 'react';
import { Mic, Square, AlertCircle } from 'lucide-react';
import { useSpeechRecognition } from '../hooks/useSpeechRecognition';

const VoiceInputButton = ({
    onTranscriptChange,
    onFinalTranscript,
    isDarkMode = false,
    disabled = false,
    className = "",
    forceVisible = false,
    alwaysVisible = false  // 新增：用于聊天页面，始终淡淡显示
}) => {
    const {
        isListening,
        transcript,
        error,
        isSupported,
        startListening,
        stopListening,
        clearTranscript,
        getFinalTranscript,
        checkSupport
    } = useSpeechRecognition();

    // 检查浏览器支持
    useEffect(() => {
        checkSupport();
    }, [checkSupport]);

    // 监听转录文本变化
    useEffect(() => {
        if (onTranscriptChange) {
            onTranscriptChange(transcript);
        }
    }, [transcript, onTranscriptChange]);

    // 处理语音识别结束
    useEffect(() => {
        if (!isListening && transcript) {
            const finalText = getFinalTranscript();
            if (finalText && onFinalTranscript) {
                onFinalTranscript(finalText);
                clearTranscript();
            }
        }
    }, [isListening, transcript, getFinalTranscript, onFinalTranscript, clearTranscript]);

    const handleClick = () => {
        if (disabled) return;
        
        if (isListening) {
            stopListening();
        } else {
            startListening();
        }
    };

    // 如果不支持语音识别，不显示按钮
    if (!isSupported) {
        return null;
    }

    // 计算透明度
    const getOpacity = () => {
        if (disabled) return 0.1;
        if (isListening) return 1;
        if (alwaysVisible) return 0.4; // 聊天页面：始终淡淡显示
        if (forceVisible) return 0.8;  // 写作页面：悬停时显示
        return 0.15; // 写作页面：默认很暗淡
    };

    const buttonStyle = {
        backgroundColor: 'transparent',
        color: isListening
            ? (isDarkMode ? '#D2691E' : '#B91C1C')
            : (isDarkMode ? '#3D342A' : '#F0E6D2'), // 使用与背景相近的颜色
        border: 'none',
        borderRadius: '50%',
        padding: '8px',
        cursor: disabled ? 'not-allowed' : 'pointer',
        opacity: getOpacity(),
        transition: 'all 0.3s ease',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minWidth: '40px',
        height: '40px'
    };

    const pulseStyle = isListening ? {
        animation: 'pulse 2s infinite'
    } : {};

    return (
        <div className={`relative ${className}`}>
            <button
                onClick={handleClick}
                disabled={disabled}
                style={{...buttonStyle, ...pulseStyle}}
                title={
                    isListening 
                        ? '点击停止录音' 
                        : '点击开始语音输入 (英语)'
                }
                className="voice-input-btn"
            >
                {isListening ? (
                    <Square className="w-4 h-4" fill="currentColor" />
                ) : (
                    <Mic className="w-5 h-5" />
                )}
            </button>

            {/* 错误提示 */}
            {error && (
                <div
                    className="absolute z-50 p-2 rounded-lg shadow-lg text-sm"
                    style={{
                        backgroundColor: isDarkMode ? '#4A3F35' : '#F5F0E6',
                        color: isDarkMode ? '#E8DCC6' : '#5D4037',
                        border: `1px solid ${isDarkMode ? '#6B5B4F' : '#D4C4A8'}`,
                        bottom: '100%',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        marginBottom: '8px',
                        minWidth: '200px',
                        maxWidth: '300px'
                    }}
                >
                    <div className="flex items-start gap-2">
                        <AlertCircle className="w-4 h-4 flex-shrink-0 mt-0.5" style={{ color: isDarkMode ? '#D2691E' : '#B91C1C' }} />
                        <span>{error}</span>
                    </div>
                    <div
                        style={{
                            position: 'absolute',
                            top: '100%',
                            left: '50%',
                            transform: 'translateX(-50%)',
                            width: '0',
                            height: '0',
                            borderLeft: '6px solid transparent',
                            borderRight: '6px solid transparent',
                            borderTop: `6px solid ${isDarkMode ? '#6B5B4F' : '#D4C4A8'}`
                        }}
                    />
                </div>
            )}

            {/* 监听状态指示 */}
            {isListening && (
                <div
                    className="absolute z-40 px-3 py-2 rounded-lg shadow-lg text-sm"
                    style={{
                        backgroundColor: isDarkMode ? '#4A3F35' : '#F5F0E6',
                        color: isDarkMode ? '#E8DCC6' : '#5D4037',
                        border: `1px solid ${isDarkMode ? '#6B5B4F' : '#D4C4A8'}`,
                        bottom: '100%',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        marginBottom: '8px',
                        minWidth: '150px'
                    }}
                >
                    <div className="flex items-center gap-2">
                        <div 
                            className="w-2 h-2 rounded-full animate-pulse" 
                            style={{ backgroundColor: isDarkMode ? '#D2691E' : '#B91C1C' }}
                        />
                        <span>正在倾听...</span>
                    </div>
                    <div
                        style={{
                            position: 'absolute',
                            top: '100%',
                            left: '50%',
                            transform: 'translateX(-50%)',
                            width: '0',
                            height: '0',
                            borderLeft: '6px solid transparent',
                            borderRight: '6px solid transparent',
                            borderTop: `6px solid ${isDarkMode ? '#6B5B4F' : '#D4C4A8'}`
                        }}
                    />
                </div>
            )}

            <style jsx>{`
                @keyframes pulse {
                    0% {
                        transform: scale(1);
                        opacity: 1;
                    }
                    50% {
                        transform: scale(1.1);
                        opacity: 0.8;
                    }
                    100% {
                        transform: scale(1);
                        opacity: 1;
                    }
                }
                
                .voice-input-btn:hover:not(:disabled) {
                    opacity: 0.8 !important;
                    transform: scale(1.1);
                    color: ${isDarkMode ? '#C4B59A' : '#8B4513'} !important;
                }
            `}</style>
        </div>
    );
};

export default VoiceInputButton;