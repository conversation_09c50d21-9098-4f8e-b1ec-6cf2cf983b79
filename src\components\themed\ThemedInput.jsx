import React from 'react';
import { useTheme } from '../ThemeProvider';

const ThemedInput = ({ 
  className = '', 
  style = {},
  size = 'md',
  ...props 
}) => {
  const { isDarkMode } = useTheme();

  // 基础样式
  const baseStyles = {
    backgroundColor: 'var(--color-bg-tertiary)',
    color: 'var(--color-text-primary)',
    border: '1px solid var(--color-border)',
    borderRadius: '12px',
    fontFamily: 'Georgia, "Noto Serif SC", serif',
    transition: 'all 0.2s ease',
    outline: 'none',
  };

  // 尺寸样式
  const sizeStyles = {
    sm: {
      padding: '12px 16px',
      fontSize: '14px',
    },
    md: {
      padding: '16px 20px',
      fontSize: '16px',
    },
    lg: {
      padding: '20px 24px',
      fontSize: '18px',
    }
  };

  // 合并样式
  const finalStyles = {
    ...baseStyles,
    ...sizeStyles[size],
    ...style,
  };

  // 焦点事件处理
  const handleFocus = (e) => {
    e.target.style.backgroundColor = isDarkMode ? 'var(--color-bg-tertiary)' : '#FFFDF0';
    e.target.style.borderColor = 'var(--color-accent-red)';
  };

  const handleBlur = (e) => {
    e.target.style.backgroundColor = 'var(--color-bg-tertiary)';
    e.target.style.borderColor = 'var(--color-border)';
  };

  return (
    <input
      className={`themed-input ${className}`}
      style={finalStyles}
      onFocus={handleFocus}
      onBlur={handleBlur}
      {...props}
    />
  );
};

export default ThemedInput;