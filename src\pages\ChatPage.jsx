import React, { useState, useRef, useEffect } from 'react';
import { Send, ArrowLeft, BookOpen, Settings, History, FileText, Share } from 'lucide-react';
import ChatHistoryModal from '../components/ChatHistoryModal';
import VoicePlayButton from '../components/VoicePlayButton';
import VoiceInputButton from '../components/VoiceInputButton';
import DiarySection from '../components/DiarySection';
import ChatEdgeNavigationArrow from '../components/ChatEdgeNavigationArrow';
import { useChat, parseAIResponse } from '../hooks/useChat';
import { useAppContext } from '../context/AppContext';
import * as chatService from '../services/chatService';

const EnglishChatPage = ({ onBackToEditor, onShowApiConfig, isDarkMode, autoPlayTTS, aiResponseSound }) => {
  const { dispatch } = useAppContext();

  const [sharedWritingContext, setSharedWritingContext] = useState(() => {
    try {
      const saved = localStorage.getItem('shared_writing_context');
      return saved ? JSON.parse(saved) : null;
    } catch (error) {
      return null;
    }
  });

  const { messages, setMessages, isLoading, handleSendMessage, handleNewConversation, messagesEndRef } = useChat(autoPlayTTS, sharedWritingContext, aiResponseSound);

  const [inputText, setInputText] = useState('');
  const [hoveredMessage, setHoveredMessage] = useState(null);
  const [showChatHistory, setShowChatHistory] = useState(false);
  const [showFloatingHeader, setShowFloatingHeader] = useState(false);
  const [suggestionBubble, setSuggestionBubble] = useState(null);
  const [isLoadingSuggestion, setIsLoadingSuggestion] = useState(false);
  const [messageSuggestions, setMessageSuggestions] = useState(() => {
    try {
      const savedSuggestions = localStorage.getItem('current_chat_suggestions');
      return savedSuggestions ? JSON.parse(savedSuggestions) : {};
    } catch (error) {
      return {};
    }
  });
  const [showWritingHistoryModal, setShowWritingHistoryModal] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [profileModalType, setProfileModalType] = useState('user');
  const [voiceTranscript, setVoiceTranscript] = useState('');
  const suggestionTimeoutRef = useRef(null);
  const headerTimeoutRef = useRef(null);
  const inputRef = useRef(null);

  const onSendMessage = () => {
    handleSendMessage(inputText);
    setInputText('');
  }

  const handleWordClick = (word) => {
    console.log('词典按钮被点击，单词:', word);
    const cleanWord = word.replace(/[^\w]/g, '').toLowerCase();
    console.log('清理后的单词:', cleanWord);
    if (cleanWord.length > 0) {
      dispatch({ type: 'SHOW_DICTIONARY', payload: true, word: cleanWord });
      console.log('词典模态框应该打开');
    }
  };

  const renderClickableText = (text) => {
    const parts = text.split(/(\s+|[^\w\s])/);
    return parts.map((part, index) => {
      if (/\w/.test(part) && part.trim().length > 0) {
        return (
          <span
            key={index}
            onClick={() => handleWordClick(part)}
            className="clickable-word"
            title={`点击查询 "${part.replace(/[^\w]/g, '')}" 的释义`}
          >
            {part}
          </span>
        );
      }
      return <span key={index}>{part}</span>;
    });
  };

  const handleSelectChatHistory = (session) => {
    setMessages(session.messages);
    setMessageSuggestions({});
    setSuggestionBubble(null);
    localStorage.removeItem('current_chat_suggestions');
  };

  const renderMarkdown = (text) => {
    if (!text) return '';
    const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    let html = '';
    let isFirstSection = true;
    lines.forEach((line) => {
      if (line.endsWith('：')) {
        const marginTop = isFirstSection ? '0px' : '8px';
        html += `<div style="font-weight: 600; margin-top: ${marginTop}; margin-bottom: 4px; color: inherit;">${line}</div>`;
        isFirstSection = false;
      } else if (line.startsWith('• ')) {
        html += `<div style="margin-left: 12px; margin-bottom: 2px; line-height: 1.3;">${line}</div>`;
      } else {
        html += `<div style="margin-bottom: 4px; line-height: 1.3;">${line}</div>`;
      }
    });
    return html;
  };

  const shareWritingToChat = (writingRecord) => {
    const contextData = {
      title: writingRecord.title || '未命名文档',
      content: writingRecord.content,
      timestamp: writingRecord.timestamp,
      wordCount: writingRecord.content.split(/\s+/).length,
      sharedAt: new Date().toISOString()
    };
    setSharedWritingContext(contextData);
    localStorage.setItem('shared_writing_context', JSON.stringify(contextData));
    const systemMessage = {
      id: Date.now(),
      type: 'system',
      content: `用户分享了一篇写作作品："${contextData.title}"（${contextData.wordCount}词）。请基于这篇文章的内容与用户进行更有针对性的对话。`,
      timestamp: new Date(),
      isContextShare: true
    };
    setMessages(prev => [...prev, systemMessage]);
    setShowWritingHistoryModal(false);
    setTimeout(async () => {
      try {
        const personalizedResponse = await chatService.generateWritingSharingResponse(contextData);
        const parsedResponse = parseAIResponse(personalizedResponse);
        const aiResponse = { id: Date.now() + 1, type: 'ai', content: parsedResponse.english, translation: parsedResponse.chinese, timestamp: new Date() };
        setMessages(prev => [...prev, aiResponse]);
      } catch (error) {
        console.error('生成个性化回复失败:', error);
        const fallbackResponse = { id: Date.now() + 1, type: 'ai', content: `Thanks for sharing your writing "${contextData.title}" with me! 📝 I can see you put a lot of thought into it. I'd love to discuss your ideas, help you refine your expressions, or explore the topics you wrote about. What aspect would you like to focus on?`, translation: `谢谢你和我分享你的作品《${contextData.title}》！📝 我能看出你投入了很多思考。我很乐意讨论你的想法，帮你完善表达，或者探讨你写的话题。你想重点关注哪个方面呢？`, timestamp: new Date() };
        setMessages(prev => [...prev, fallbackResponse]);
      }
    }, 1000);
  };

  const clearWritingContext = () => {
    setSharedWritingContext(null);
    localStorage.removeItem('shared_writing_context');
    const clearMessage = { id: Date.now(), type: 'ai', content: "Got it! I've cleared the writing context. We can now chat about anything you'd like! 😊", translation: "明白了！我已经清除了写作上下文。现在我们可以聊任何你想聊的话题！😊", timestamp: new Date() };
    setMessages(prev => [...prev, clearMessage]);
  };

  const handleAvatarClick = (type) => {
    setProfileModalType(type);
    setShowProfileModal(true);
  };

  const handleShareWritingFromProfile = () => {
    setShowWritingHistoryModal(true);
  };

  // 处理与日记聊天
  const handleChatWithDiary = (diary) => {
    // 关闭个人信息模态框
    setShowProfileModal(false);

    // 创建基于日记的上下文消息
    const diaryContextMessage = {
      id: Date.now(),
      type: 'user',
      content: `I'd like to discuss this diary entry from ${diary.date}:\n\n"${diary.english}"\n\nCan you help me explore this topic further?`,
      timestamp: new Date(),
      isFromDiary: true,
      diaryDate: diary.date
    };

    // 添加到消息列表并自动发送
    setMessages(prev => [...prev, diaryContextMessage]);

    // 自动发送消息给AI
    setTimeout(() => {
      handleSendMessage(diaryContextMessage.content);
    }, 100);
  };

  const handleUserMessageClick = async (message, event) => {
    if (isLoadingSuggestion) return;
    if (suggestionTimeoutRef.current) {
      clearTimeout(suggestionTimeoutRef.current);
    }
    const rect = event.currentTarget.getBoundingClientRect();
    const position = { x: rect.left, y: rect.top - 10 };
    if (messageSuggestions[message.id]) {
      setSuggestionBubble({ messageId: message.id, content: messageSuggestions[message.id], position, isLoading: false });
      return;
    }
    setIsLoadingSuggestion(true);
    setSuggestionBubble({ messageId: message.id, content: '正在生成建议...', position, isLoading: true });
    try {
      const suggestion = await chatService.getExpressionSuggestion(message.content);
      setMessageSuggestions(prev => {
        const newSuggestions = { ...prev, [message.id]: suggestion };
        localStorage.setItem('current_chat_suggestions', JSON.stringify(newSuggestions));
        return newSuggestions;
      });
      setSuggestionBubble({ messageId: message.id, content: suggestion, position, isLoading: false });
    } catch (error) {
      console.error('Failed to get suggestion:', error);
      setSuggestionBubble({ messageId: message.id, content: '获取建议失败，请重试。', position, isLoading: false });
    } finally {
      setIsLoadingSuggestion(false);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (suggestionBubble && !event.target.closest('.suggestion-bubble')) {
        setSuggestionBubble(null);
      }
    };
    if (suggestionBubble) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [suggestionBubble]);

  const handleHeaderMouseEnter = () => {
    if (headerTimeoutRef.current) {
      clearTimeout(headerTimeoutRef.current);
    }
    setShowFloatingHeader(true);
  };

  const handleHeaderMouseLeave = () => {
    headerTimeoutRef.current = setTimeout(() => {
      setShowFloatingHeader(false);
    }, 300);
  };

  // 处理语音输入转录变化
  const handleVoiceTranscriptChange = (transcript) => {
    setVoiceTranscript(transcript);
  };

  // 处理语音输入完成
  const handleVoiceFinalTranscript = (finalTranscript) => {
    if (finalTranscript.trim()) {
      // 将语音转录的文本添加到输入框
      const newText = inputText ? `${inputText} ${finalTranscript}` : finalTranscript;
      setInputText(newText);
      
      // 聚焦到输入框
      if (inputRef.current) {
        inputRef.current.focus();
        // 将光标移到末尾
        setTimeout(() => {
          inputRef.current.setSelectionRange(newText.length, newText.length);
        }, 0);
      }
    }
    setVoiceTranscript('');
  };

  return (
    <div
      className="min-h-screen transition-colors duration-300"
      style={{ backgroundColor: isDarkMode ? '#1A1611' : '#F5EFE6' }}
    >
      <div
        className="fixed top-0 left-0 right-0 z-40"
        style={{ height: '80px' }}
        onMouseEnter={handleHeaderMouseEnter}
        onMouseLeave={handleHeaderMouseLeave}
      />

      <div
        className="fixed top-0 left-0 right-0 z-50 transition-all duration-300 border-b"
        style={{
          backgroundColor: isDarkMode ? '#2A241D' : '#F0E6D2',
          borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
          transform: showFloatingHeader ? 'translateY(0)' : 'translateY(-100%)',
          boxShadow: showFloatingHeader
            ? (isDarkMode ? '0 4px 12px rgba(0, 0, 0, 0.3)' : '0 4px 12px rgba(93, 64, 55, 0.15)')
            : 'none'
        }}
        onMouseEnter={handleHeaderMouseEnter}
        onMouseLeave={handleHeaderMouseLeave}
      >
        <div className="max-w-7xl mx-auto py-6">
          <div className="flex items-center justify-between" style={{ paddingLeft: '80px', paddingRight: '80px' }}>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div
                  className="flex items-center justify-center w-12 h-12 rounded-xl transition-colors duration-300"
                  style={{ backgroundColor: isDarkMode ? '#D2691E' : '#B91C1C' }}
                >
                  <BookOpen className="w-7 h-7" style={{ color: '#FEFCF5' }} />
                </div>
                <div>
                  <h1
                    className="text-3xl font-bold transition-colors duration-300"
                    style={{
                      color: isDarkMode ? '#E8DCC6' : '#5D4037',
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      letterSpacing: '0.05em'
                    }}
                  >
                    English Chat
                  </h1>
                  <p
                    className="transition-colors duration-300"
                    style={{
                      color: isDarkMode ? '#C4B59A' : '#8B4513',
                      fontSize: '16px',
                      marginTop: '4px',
                      letterSpacing: '0.05em'
                    }}
                  >
                    Practice English conversation
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <button
                onClick={() => {
                  console.log('聊天历史按钮被点击');
                  setShowChatHistory(true);
                }}
                className="header-btn"
                title="聊天历史"
              >
                <History className="w-6 h-6" />
              </button>
              <button
                onClick={() => {
                  console.log('设置按钮被点击');
                  console.log('onShowApiConfig函数:', onShowApiConfig);
                  if (onShowApiConfig) {
                    onShowApiConfig();
                  } else {
                    console.error('onShowApiConfig函数未定义');
                  }
                }}
                className="header-btn"
                title="设置"
              >
                <Settings className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto flex flex-col" style={{ height: '100vh' }}>
        <div className="flex-1 overflow-y-auto p-6 pt-8 space-y-4 chat-scrollbar">
          {sharedWritingContext && (
            <div
              className="mb-4 p-4 rounded-xl border transition-all duration-300"
              style={{
                backgroundColor: isDarkMode ? '#2A241D' : '#F0E6D2',
                borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
                color: isDarkMode ? '#E8DCC6' : '#5D4037'
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <FileText className="w-5 h-5" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                  <div>
                    <div
                      className="font-medium"
                      style={{
                        fontFamily: 'Georgia, "Noto Serif SC", serif',
                        fontSize: '16px'
                      }}
                    >
                      写作上下文：{sharedWritingContext.title}
                    </div>
                    <div
                      className="text-sm mt-1"
                      style={{
                        color: isDarkMode ? '#C4B59A' : '#8B4513',
                        fontFamily: 'Georgia, "Noto Serif SC", serif'
                      }}
                    >
                      {sharedWritingContext.wordCount} 词 • {new Date(sharedWritingContext.sharedAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <button
                  onClick={clearWritingContext}
                  className="p-2 rounded-lg transition-colors duration-200"
                  style={{
                    color: isDarkMode ? '#C4B59A' : '#8B4513',
                    backgroundColor: 'transparent'
                  }}
                  title="清除写作上下文"
                >
                  ✕
                </button>
              </div>
            </div>
          )}

          {messages.filter(message => message.type !== 'system').map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} items-start gap-3`}
            >
              {message.type === 'ai' && (
                <div
                  className="chat-avatar alex-avatar flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 cursor-pointer"
                  title="点击查看 Alex 的个人信息"
                  onClick={() => handleAvatarClick('alex')}
                >
                  <span style={{ fontSize: '20px' }}>🌿</span>
                </div>
              )}

              <div className="relative flex flex-col">
                <div
                  className="max-w-xs lg:max-w-md px-4 py-3 rounded-2xl transition-all duration-300 cursor-pointer chat-message-text"
                  style={{
                    backgroundColor: message.type === 'user'
                      ? (isDarkMode ? '#D2691E' : '#166534')
                      : (isDarkMode ? '#332B22' : '#FFFEF7'),
                    color: message.type === 'user'
                      ? '#FEFCF5'
                      : (isDarkMode ? '#E8DCC6' : '#5D4037'),
                    lineHeight: '1.6',
                    border: message.type === 'ai' ? `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}` : 'none',
                    transform: hoveredMessage === message.id && message.type === 'ai' ? 'scale(1.02)' : 'scale(1)',
                    opacity: message.type === 'user' && isLoadingSuggestion ? 0.7 : 1,
                    position: 'relative',
                    borderRadius: message.type === 'user' ? '20px 20px 4px 20px' : '20px 20px 20px 4px'
                  }}
                  onMouseEnter={(e) => {
                    if (suggestionTimeoutRef.current) {
                      clearTimeout(suggestionTimeoutRef.current);
                    }
                    if (message.type === 'ai') {
                      setHoveredMessage(message.id);
                    } else if (message.type === 'user' && messageSuggestions[message.id]) {
                      const rect = e.currentTarget.getBoundingClientRect();
                      const position = { x: rect.left, y: rect.top - 10 };
                      setSuggestionBubble({ messageId: message.id, content: messageSuggestions[message.id], position, isLoading: false });
                    }
                  }}
                  onMouseLeave={() => {
                    setHoveredMessage(null);
                    if (message.type === 'user' && messageSuggestions[message.id] && suggestionBubble?.messageId === message.id) {
                      suggestionTimeoutRef.current = setTimeout(() => {
                        setSuggestionBubble(null);
                      }, 300);
                    }
                  }}
                  onClick={(e) => {
                    if (message.type === 'user') {
                      handleUserMessageClick(message, e);
                    }
                  }}
                  title={
                    message.type === 'user'
                      ? messageSuggestions[message.id]
                        ? ''
                        : '点击获取表达建议'
                      : ''
                  }
                >
                  <div>
                    <div className="inline">
                      {message.type === 'ai' ? renderClickableText(message.content) : message.content}
                    </div>
                    {message.type === 'user' && messageSuggestions[message.id] && (
                      <span
                        className="inline-block ml-2 opacity-60"
                        style={{ fontSize: '12px', color: '#FEFCF5' }}
                        title="已有表达建议"
                      >
                        💡
                      </span>
                    )}
                    {message.type === 'ai' && (
                      <span className="inline-block ml-2 align-bottom">
                        <VoicePlayButton
                          text={message.content}
                          isDarkMode={isDarkMode}
                          size="small"
                        />
                      </span>
                    )}
                  </div>
                </div>
                {message.type === 'ai' && message.translation && hoveredMessage === message.id && (
                  <div
                    className="absolute z-10 px-3 py-2 rounded-xl shadow-lg transition-all duration-200"
                    style={{
                      backgroundColor: isDarkMode ? '#4A3F35' : '#F5F0E6',
                      color: isDarkMode ? '#E8DCC6' : '#5D4037',
                      border: `1px solid ${isDarkMode ? '#6B5B4F' : '#D4C4A8'}`,
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      fontSize: '14px',
                      lineHeight: '1.5',
                      maxWidth: '280px',
                      minWidth: '200px',
                      top: '0',
                      left: '100%',
                      marginLeft: '12px',
                      boxShadow: isDarkMode
                        ? '0 4px 12px rgba(0, 0, 0, 0.3)'
                        : '0 4px 12px rgba(93, 64, 55, 0.15)'
                    }}
                  >
                    <div
                      className="text-xs mb-1"
                      style={{ color: isDarkMode ? '#C4B59A' : '#8B4513', fontWeight: '500' }}
                    >
                      中文翻译：
                    </div>
                    {message.translation}
                    <div
                      style={{
                        position: 'absolute',
                        top: '16px',
                        left: '-6px',
                        width: '12px',
                        height: '12px',
                        backgroundColor: isDarkMode ? '#4A3F35' : '#F5F0E6',
                        border: `1px solid ${isDarkMode ? '#6B5B4F' : '#D4C4A8'}`,
                        borderTop: 'none',
                        borderRight: 'none',
                        transform: 'rotate(45deg)'
                      }}
                    />
                  </div>
                )}
              </div>

              {message.type === 'user' && (
                <div
                  className="chat-avatar user-avatar flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 cursor-pointer"
                  title="点击查看个人信息和分享写作"
                  onClick={() => handleAvatarClick('user')}
                >
                  <span style={{ fontSize: '18px' }}>👤</span>
                </div>
              )}
            </div>
          ))}

          {isLoading && (
            <div className="flex justify-start items-start gap-3">
              <div
                className="chat-avatar alex-avatar flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300"
              >
                <span style={{ fontSize: '20px' }}>🌿</span>
              </div>
              <div
                className="max-w-xs lg:max-w-md px-4 py-3 rounded-2xl transition-colors duration-300"
                style={{
                  backgroundColor: isDarkMode ? '#332B22' : '#FFFEF7',
                  color: isDarkMode ? '#E8DCC6' : '#5D4037',
                  fontFamily: 'Georgia, "Noto Serif SC", serif',
                  border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`,
                  borderRadius: '20px 20px 20px 4px'
                }}
              >
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: isDarkMode ? '#D2691E' : '#166534' }}></div>
                    <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: isDarkMode ? '#D2691E' : '#166534', animationDelay: '0.2s' }}></div>
                    <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: isDarkMode ? '#D2691E' : '#166534', animationDelay: '0.4s' }}></div>
                  </div>
                  <span className="text-sm">Thinking...</span>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        <div className="p-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleNewConversation}
              disabled={isLoading}
              className="new-chat-btn"
              title="开始新对话"
            >
              {isLoading ? (
                <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
              )}
            </button>

            <div className="flex-1 relative">
              <textarea
                ref={inputRef}
                value={voiceTranscript ? `${inputText}${voiceTranscript}` : inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    onSendMessage();
                  }
                }}
                placeholder="Type your message in English..."
                className="w-full resize-none rounded-xl focus:outline-none"
                style={{
                  backgroundColor: isDarkMode ? '#332B22' : '#FFFEF7',
                  color: isDarkMode ? '#E8DCC6' : '#5D4037',
                  fontFamily: 'Georgia, "Noto Serif SC", serif',
                  padding: '16px 60px 16px 20px', // 右侧留出空间给语音按钮
                  fontSize: '16px',
                  border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`,
                  minHeight: '48px',
                  maxHeight: '120px',
                  lineHeight: '1.5',
                  transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease'
                }}
                rows={2}
              />
              
              {/* 语音输入按钮 */}
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <VoiceInputButton
                  onTranscriptChange={handleVoiceTranscriptChange}
                  onFinalTranscript={handleVoiceFinalTranscript}
                  isDarkMode={isDarkMode}
                  disabled={isLoading}
                  alwaysVisible={true}
                />
              </div>
              
              {/* 语音转录预览 */}
              {voiceTranscript && (
                <div
                  className="absolute bottom-full left-0 right-0 p-2 rounded-t-xl text-sm"
                  style={{
                    backgroundColor: isDarkMode ? '#4A3F35' : '#F0E6D2',
                    color: isDarkMode ? '#C4B59A' : '#8B4513',
                    border: `1px solid ${isDarkMode ? '#6B5B4F' : '#E6D7B8'}`,
                    borderBottom: 'none',
                    fontFamily: 'Georgia, "Noto Serif SC", serif',
                    fontStyle: 'italic'
                  }}
                >
                  正在倾听: {voiceTranscript}
                </div>
              )}
            </div>
            <button
              onClick={onSendMessage}
              disabled={!inputText.trim() || isLoading}
              className="send-btn"
            >
              <Send className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>



      {suggestionBubble && (
        <div
          className="suggestion-bubble custom-scrollbar fixed z-50 max-w-sm p-4 rounded-xl shadow-lg transition-all duration-200"
          onMouseEnter={() => {
            if (suggestionTimeoutRef.current) {
              clearTimeout(suggestionTimeoutRef.current);
            }
          }}
          onMouseLeave={() => {
            suggestionTimeoutRef.current = setTimeout(() => {
              setSuggestionBubble(null);
            }, 300);
          }}
          style={{
            left: `${suggestionBubble.position.x}px`,
            top: `${suggestionBubble.position.y}px`,
            transform: 'translateY(-100%)',
            backgroundColor: isDarkMode ? '#4A3F35' : '#F5F0E6',
            color: isDarkMode ? '#E8DCC6' : '#5D4037',
            border: `1px solid ${isDarkMode ? '#6B5B4F' : '#D4C4A8'}`,
            fontFamily: 'Georgia, "Noto Serif SC", serif',
            fontSize: '14px',
            lineHeight: '1.5',
            boxShadow: isDarkMode
              ? '0 8px 24px rgba(0, 0, 0, 0.4)'
              : '0 8px 24px rgba(93, 64, 55, 0.2)',
            maxHeight: '300px',
            overflowY: 'auto'
          }}
        >
          <div className="flex items-start justify-between mb-2">
            <div
              className="text-xs font-medium"
              style={{ color: isDarkMode ? '#D2691E' : '#166534' }}
            >
              表达建议
            </div>
            <button
              onClick={() => setSuggestionBubble(null)}
              className="ml-2 text-xs opacity-60 hover:opacity-100 transition-opacity"
              style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}
            >
              ✕
            </button>
          </div>

          <div>
            {suggestionBubble.isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: isDarkMode ? '#D2691E' : '#166534' }}></div>
                <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: isDarkMode ? '#D2691E' : '#166534', animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: isDarkMode ? '#D2691E' : '#166534', animationDelay: '0.4s' }}></div>
                <span className="text-sm">正在生成建议...</span>
              </div>
            ) : (
              <div
                dangerouslySetInnerHTML={{
                  __html: renderMarkdown(suggestionBubble.content)
                }}
                style={{
                  color: isDarkMode ? '#E8DCC6' : '#5D4037'
                }}
              />
            )}
          </div>
        </div>
      )}

      {showWritingHistoryModal && (
        <WritingHistoryModal
          isOpen={showWritingHistoryModal}
          onClose={() => setShowWritingHistoryModal(false)}
          onShareWriting={shareWritingToChat}
          isDarkMode={isDarkMode}
        />
      )}

      <ProfileModal
        isOpen={showProfileModal}
        onClose={() => setShowProfileModal(false)}
        profileType={profileModalType}
        isDarkMode={isDarkMode}
        onShareWriting={handleShareWritingFromProfile}
        autoPlayTTS={autoPlayTTS}
        onChatWithDiary={handleChatWithDiary}
      />

      <ChatHistoryModal
        isOpen={showChatHistory}
        onClose={() => setShowChatHistory(false)}
        onSelectSession={handleSelectChatHistory}
        isDarkMode={isDarkMode}
      />

      {/* 边缘导航箭头 - 返回写作模式 */}
      <ChatEdgeNavigationArrow
        onBackToEditor={onBackToEditor}
        isDarkMode={isDarkMode}
      />
    </div>
  );
};

const WritingHistoryModal = ({ isOpen, onClose, onShareWriting, isDarkMode }) => {
  const [writingHistory, setWritingHistory] = useState([]);
  const [selectedWriting, setSelectedWriting] = useState(null);

  useEffect(() => {
    if (isOpen) {
      try {
        const history = localStorage.getItem('writing_history');
        const parsedHistory = history ? JSON.parse(history) : [];
        setWritingHistory(parsedHistory.slice(0, 10));
      } catch (error) {
        console.error('获取写作历史失败:', error);
        setWritingHistory([]);
      }
    }
  }, [isOpen]);

  const handleShare = () => {
    if (selectedWriting) {
      onShareWriting(selectedWriting);
      setSelectedWriting(null);
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{ backgroundColor: 'var(--modal-backdrop)' }}
    >
      <div
        className="w-full max-w-2xl max-h-[80vh] rounded-2xl shadow-2xl overflow-hidden"
        style={{
          backgroundColor: isDarkMode ? '#2A241D' : '#F0E6D2',
          border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`
        }}
      >
        <div
          className="flex items-center justify-between p-6 border-b"
          style={{ borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8' }}
        >
          <div className="flex items-center gap-3">
            <FileText className="w-6 h-6" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
            <h2
              className="text-2xl font-bold"
              style={{
                color: isDarkMode ? '#E8DCC6' : '#5D4037',
                fontFamily: 'Georgia, "Noto Serif SC", serif'
              }}
            >
              分享写作历史
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg transition-colors duration-200"
            style={{
              color: isDarkMode ? '#C4B59A' : '#8B4513',
              backgroundColor: 'transparent'
            }}
          >
            ✕
          </button>
        </div>

        <div className="flex-1 overflow-y-auto custom-scrollbar p-6">
          {writingHistory.length === 0 ? (
            <div
              className="text-center py-12"
              style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}
            >
              <FileText className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg">暂无写作历史</p>
              <p className="text-sm mt-2">完成写作后，历史记录会显示在这里</p>
            </div>
          ) : (
            <div className="space-y-3">
              <p
                className="text-sm mb-4"
                style={{
                  color: isDarkMode ? '#C4B59A' : '#8B4513',
                  fontFamily: 'Georgia, "Noto Serif SC", serif'
                }}
              >
                选择一篇写作作品分享给AI，它将基于你的写作内容进行更有针对性的对话：
              </p>

              {writingHistory.map((writing, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${selectedWriting === writing ? 'border-opacity-100' : 'border-opacity-30'}`}
                  style={{
                    backgroundColor: selectedWriting === writing
                      ? (isDarkMode ? '#332B22' : '#FFFEF7')
                      : (isDarkMode ? '#1A1611' : '#F5EFE6'),
                    borderColor: selectedWriting === writing
                      ? (isDarkMode ? '#D2691E' : '#166534')
                      : (isDarkMode ? '#4A3F35' : '#E6D7B8')
                  }}
                  onClick={() => setSelectedWriting(writing)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3
                        className="font-medium mb-2"
                        style={{
                          color: isDarkMode ? '#E8DCC6' : '#5D4037',
                          fontFamily: 'Georgia, "Noto Serif SC", serif'
                        }}
                      >
                        {writing.title || '未命名文档'}
                      </h3>
                      <p
                        className="text-sm mb-2 line-clamp-2"
                        style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}
                      >
                        {writing.content.substring(0, 100)}...
                      </p>
                      <div
                        className="text-xs flex items-center gap-4"
                        style={{ color: isDarkMode ? '#A0916B' : '#9B6B47' }}
                      >
                        <span>{writing.content.split(/\s+/).length} 词</span>
                        <span>{new Date(writing.timestamp).toLocaleDateString()}</span>
                      </div>
                    </div>
                    {selectedWriting === writing && (
                      <Share
                        className="w-5 h-5 ml-3"
                        style={{ color: isDarkMode ? '#D2691E' : '#166534' }}
                      />
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {writingHistory.length > 0 && (
          <div
            className="flex items-center justify-end gap-3 p-6 border-t"
            style={{ borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8' }}
          >
            <button
              onClick={onClose}
              className="px-6 py-2 rounded-xl transition-colors duration-200"
              style={{
                color: isDarkMode ? '#C4B59A' : '#8B4513',
                backgroundColor: 'transparent',
                border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`
              }}
            >
              取消
            </button>
            <button
              onClick={handleShare}
              disabled={!selectedWriting}
              className="px-6 py-2 rounded-xl transition-colors duration-200 disabled:opacity-50"
              style={{
                backgroundColor: selectedWriting
                  ? (isDarkMode ? '#D2691E' : '#166534')
                  : (isDarkMode ? '#4A3F35' : '#E6D7B8'),
                color: '#FEFCF5',
                border: 'none'
              }}
            >
              分享到聊天
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

const ProfileModal = ({ isOpen, onClose, profileType, isDarkMode, onShareWriting, autoPlayTTS, onChatWithDiary }) => {
  const [showDiary, setShowDiary] = useState(false);
  const [highlightDiaryId, setHighlightDiaryId] = useState(null);
  const generateDiaryFnRef = useRef(null);

  if (!isOpen) return null;

  const isAlex = profileType === 'alex';

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{ backgroundColor: 'var(--modal-backdrop)' }}
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <div
        className="w-full max-w-md rounded-2xl shadow-2xl overflow-hidden"
        style={{
          backgroundColor: isDarkMode ? '#2A241D' : '#F0E6D2',
          border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`
        }}
      >
        {!showDiary && (
          <>
            <div
              className="relative h-32"
              style={{
                background: isAlex
                  ? 'linear-gradient(135deg, #2D5A2D, #4A7C59, #6B8E6B)'
                  : 'linear-gradient(135deg, #8B4513, #D2691E, #CD853F)'
              }}
            >
              <div className="absolute inset-0 opacity-20">
                {isAlex ? (
                  <div className="flex items-center justify-center h-full text-6xl">🌿🌺🍃</div>
                ) : (
                  <div className="flex items-center justify-center h-full text-6xl">✨📝💭</div>
                )}
              </div>
            </div>

            <div className="relative -mt-12 flex justify-center">
              <div
                className="w-20 h-20 rounded-full flex items-center justify-center border-4"
                style={{
                  backgroundColor: isDarkMode ? '#332B22' : '#FFFEF7',
                  borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
                }}
              >
                <span style={{ fontSize: '32px' }}>
                  {isAlex ? '🌿' : '👤'}
                </span>
              </div>
            </div>
          </>
        )}

        {showDiary && isAlex && (
          <div
            className="flex items-center justify-between p-4 border-b"
            style={{
              backgroundColor: isDarkMode ? '#332B22' : '#F9F7F4',
              borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8'
            }}
          >
            <button
              onClick={() => setShowDiary(false)}
              className="p-2 rounded-lg transition-colors duration-200"
              style={{
                color: isDarkMode ? '#C4B59A' : '#8B4513',
                backgroundColor: 'transparent'
              }}
              title="返回个人信息"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h2
              className="text-lg font-bold"
              style={{
                color: isDarkMode ? '#E8DCC6' : '#5D4037',
                fontFamily: 'Georgia, "Noto Serif SC", serif'
              }}
            >
              Alex's Daily Journal
            </h2>
            <button
              onClick={() => {
                if (generateDiaryFnRef.current) {
                  generateDiaryFnRef.current();
                }
              }}
              className="flex items-center gap-1 px-3 py-1.5 rounded-lg transition-colors duration-200"
              style={{
                backgroundColor: isDarkMode ? '#D2691E' : '#166534',
                color: '#FEFCF5',
                fontSize: '12px'
              }}
              title="生成新日记"
            >
              <span>🌿</span>
              <span>新日记</span>
            </button>
          </div>
        )}

        {!showDiary && (
          <div className="px-6 pt-2 pb-4">
            <div className="text-center mb-4">
              <h2
                className="text-2xl font-bold mb-2"
                style={{
                  color: isDarkMode ? '#E8DCC6' : '#5D4037',
                  fontFamily: 'Georgia, "Noto Serif SC", serif'
                }}
              >
                {isAlex ? 'Alex' : '用户'}
              </h2>
              <p
                className="text-sm mb-4"
                style={{
                  color: isDarkMode ? '#C4B59A' : '#8B4513',
                  fontFamily: 'Georgia, "Noto Serif SC", serif'
                }}
              >
                {isAlex
                  ? '植物学家 & 自然摄影师 🌿📸'
                  : '英语学习者 📚✨'
                }
              </p>
            </div>
          </div>
        )}

        <div className={showDiary ? "" : "px-6 pb-6"}>
          {isAlex ? (
            showDiary ? (
              <div className="overflow-y-auto custom-scrollbar" style={{ maxHeight: 'calc(80vh - 80px)' }}>
                <div className="p-4">
                  <DiarySection
                    isDarkMode={isDarkMode}
                    autoPlayTTS={autoPlayTTS}
                    isInModal={true}
                    isCompact={false}
                    highlightDiaryId={highlightDiaryId}
                    onGenerateDiary={(fn) => {
                      generateDiaryFnRef.current = fn;
                    }}
                    onChatWithDiary={onChatWithDiary}
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* AI生成图像缩略图网格 */}
                <RecentImagesGrid isDarkMode={isDarkMode} onImageClick={(diaryId) => {
                  // 点击图片跳转到对应的日记并滚动到该位置
                  setHighlightDiaryId(diaryId);
                  setShowDiary(true);
                }} />


              </div>
            )
          ) : (
            <div className="space-y-4">
              <div className="flex items-center gap-3 py-2">
                <span style={{ fontSize: '18px' }}>🎯</span>
                <div
                  className="text-sm"
                  style={{
                    color: isDarkMode ? '#C4B59A' : '#8B4513',
                    fontFamily: 'Georgia, "Noto Serif SC", serif'
                  }}
                >
                  提高英语表达能力
                </div>
              </div>

              <div className="flex items-center gap-3 py-2">
                <span style={{ fontSize: '18px' }}>💬</span>
                <div
                  className="text-sm"
                  style={{
                    color: isDarkMode ? '#C4B59A' : '#8B4513',
                    fontFamily: 'Georgia, "Noto Serif SC", serif'
                  }}
                >
                  与AI进行英语对话练习
                </div>
              </div>

              <div className="flex items-center gap-3 py-2">
                <span style={{ fontSize: '18px' }}>📚</span>
                <div
                  className="text-sm"
                  style={{
                    color: isDarkMode ? '#C4B59A' : '#8B4513',
                    fontFamily: 'Georgia, "Noto Serif SC", serif'
                  }}
                >
                  使用词典查询功能学习新词汇
                </div>
              </div>

              <div className="flex items-center gap-3 py-2">
                <span style={{ fontSize: '18px' }}>📝</span>
                <div
                  className="text-sm"
                  style={{
                    color: isDarkMode ? '#C4B59A' : '#8B4513',
                    fontFamily: 'Georgia, "Noto Serif SC", serif'
                  }}
                >
                  分享你的写作作品获得反馈
                </div>
              </div>

              <button
                onClick={() => {
                  onClose();
                  onShareWriting();
                }}
                className="w-full mt-4 py-3 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center gap-2"
                style={{
                  backgroundColor: isDarkMode ? '#D2691E' : '#166534',
                  color: '#FEFCF5',
                  fontFamily: 'Georgia, "Noto Serif SC", serif',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                <FileText className="w-4 h-4" />
                分享写作历史
              </button>
            </div>
          )}
        </div>
      </div>


    </div>
  );
};

// 最近AI生成图像缩略图网格组件
const RecentImagesGrid = ({ isDarkMode, onImageClick }) => {
  const [recentImages, setRecentImages] = useState([]);

  const loadImages = () => {
    try {
      const savedImages = localStorage.getItem('generated_images');
      if (savedImages) {
        const imagesData = JSON.parse(savedImages);
        // 转换为数组并按时间排序，取最近的6张
        const imageArray = Object.entries(imagesData)
          .map(([diaryId, data]) => {
            // 处理时间戳格式（可能是字符串或数字）
            let timestamp = data.timestamp || Date.now();
            if (typeof timestamp === 'string') {
              timestamp = new Date(timestamp).getTime();
            }
            return {
              diaryId,
              url: data.url,
              timestamp
            };
          })
          .sort((a, b) => {
            // 首先按时间戳排序
            const timeDiff = b.timestamp - a.timestamp;
            if (timeDiff !== 0) return timeDiff;
            // 如果时间戳相同，按日记ID排序（更大的ID代表更新的日记）
            return parseInt(b.diaryId) - parseInt(a.diaryId);
          })
          .slice(0, 6);

        setRecentImages(imageArray);
      }
    } catch (error) {
      console.error('加载图像失败:', error);
    }
  };

  useEffect(() => {
    loadImages();

    // 监听localStorage变化
    const handleStorageChange = (e) => {
      if (e.key === 'generated_images') {
        loadImages();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // 定期刷新（防止同页面内的更新不被监听到）
    const interval = setInterval(loadImages, 2000);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  if (recentImages.length === 0) {
    return (
      <div
        className="text-center py-12 px-4"
        style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}
      >
        <div
          className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
          style={{
            backgroundColor: isDarkMode ? '#2D2520' : '#FAF7F0',
            border: `2px dashed ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`
          }}
        >
          <div className="text-2xl">🌿</div>
        </div>
        <div className="text-sm opacity-75 mb-1">还没有AI生成的图像</div>
        <div className="text-xs opacity-50">生成日记时会自动创建精美图像</div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-3 gap-3">
      {recentImages.map((image, index) => (
        <div
          key={image.diaryId}
          className="group relative aspect-square rounded-xl overflow-hidden cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-lg"
          style={{
            backgroundColor: isDarkMode ? '#2D2520' : '#FAF7F0',
            boxShadow: isDarkMode
              ? '0 2px 8px rgba(0, 0, 0, 0.3), 0 1px 3px rgba(0, 0, 0, 0.2)'
              : '0 2px 8px rgba(139, 69, 19, 0.1), 0 1px 3px rgba(139, 69, 19, 0.05)',
            border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`
          }}
          onClick={() => onImageClick(image.diaryId)}
        >
          <img
            src={image.url}
            alt={`AI生成图像 ${index + 1}`}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
            onError={(e) => {
              e.target.style.display = 'none';
            }}
          />

          {/* 悬停时的遮罩层 */}
          <div
            className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          />

          {/* 悬停时的提示文字 */}
          <div
            className="absolute bottom-2 left-2 right-2 text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-center"
            style={{
              textShadow: '0 1px 2px rgba(0, 0, 0, 0.8)',
              fontFamily: 'Georgia, serif'
            }}
          >
            View Journal
          </div>

          {/* 图片序号标识 */}
          {index === 0 && (
            <div
              className="absolute top-2 right-2 px-1.5 py-0.5 rounded-full flex items-center justify-center text-xs font-medium"
              style={{
                backgroundColor: isDarkMode ? '#D2691E' : '#166534',
                color: '#FEFCF5',
                fontSize: '9px',
                fontFamily: 'Arial, sans-serif',
                fontWeight: 'bold'
              }}
            >
              NEW
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default EnglishChatPage;