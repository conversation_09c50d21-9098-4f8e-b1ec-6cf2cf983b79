import React, { useState, useEffect, useCallback } from 'react';
import { Play, Pause, Square, Volume2, VolumeX } from 'lucide-react';
import { ttsService } from '../services/ttsService';

const VoicePlayButton = ({ 
  text, 
  isDarkMode, 
  size = 'small',
  className = '',
  onPlayStart = null,
  onPlayEnd = null,
  onError = null
}) => {
  const [playState, setPlayState] = useState({
    isPlaying: false,
    isPaused: false,
    isLoading: false,
    error: null
  });

  // 检查TTS支持
  const [isSupported, setIsSupported] = useState(ttsService.status.isSupported);

  useEffect(() => {
    setIsSupported(ttsService.status.isSupported);
  }, []);

  // 清理函数
  const cleanup = useCallback(() => {
    setPlayState({
      isPlaying: false,
      isPaused: false,
      isLoading: false,
      error: null
    });
  }, []);

  // 处理播放/暂停
  const handlePlayPause = async () => {
    if (!isSupported) {
      const error = '您的浏览器不支持语音播放功能';
      setPlayState(prev => ({ ...prev, error }));
      onError?.(error);
      return;
    }

    if (!text || !text.trim()) {
      const error = '没有可播放的文本内容';
      setPlayState(prev => ({ ...prev, error }));
      onError?.(error);
      return;
    }

    try {
      const currentStatus = ttsService.status;

      if (currentStatus.isPlaying) {
        // 当前正在播放，暂停
        ttsService.pause();
        setPlayState(prev => ({ ...prev, isPlaying: false, isPaused: true }));
      } else if (currentStatus.isPaused) {
        // 当前暂停，恢复播放
        ttsService.resume();
        setPlayState(prev => ({ ...prev, isPlaying: true, isPaused: false }));
      } else {
        // 开始新的播放
        setPlayState(prev => ({ ...prev, isLoading: true, error: null }));
        onPlayStart?.(text);

        try {
          await ttsService.speak(text, {
            lang: 'en-US',
            rate: 0.9,
            pitch: 1.0,
            volume: 1.0
          });

          // 播放完成
          cleanup();
          onPlayEnd?.(text);
        } catch (error) {
          console.error('TTS播放失败:', error);
          const errorMsg = error.message || '语音播放失败';
          setPlayState(prev => ({ ...prev, error: errorMsg, isLoading: false }));
          onError?.(errorMsg);
        }
      }
    } catch (error) {
      console.error('TTS操作失败:', error);
      const errorMsg = error.message || '语音操作失败';
      setPlayState(prev => ({ ...prev, error: errorMsg, isLoading: false }));
      onError?.(errorMsg);
    }
  };

  // 停止播放
  const handleStop = () => {
    ttsService.stop();
    cleanup();
  };

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      // 如果当前组件的文本正在播放，停止播放
      if (ttsService.status.isPlaying) {
        ttsService.stop();
      }
    };
  }, []);

  // 样式配置
  const sizeConfig = {
    small: {
      button: 'w-8 h-8',
      icon: 'w-4 h-4'
    },
    medium: {
      button: 'w-10 h-10',
      icon: 'w-5 h-5'
    },
    large: {
      button: 'w-12 h-12',
      icon: 'w-6 h-6'
    }
  };

  const config = sizeConfig[size] || sizeConfig.small;

  // 获取当前图标
  const getCurrentIcon = () => {
    if (playState.isLoading) {
      return (
        <div 
          className="animate-spin rounded-full border-2 border-current border-t-transparent"
          style={{ width: '16px', height: '16px' }}
        />
      );
    }

    if (!isSupported) {
      return <VolumeX className={config.icon} />;
    }

    if (playState.isPlaying && !playState.isPaused) {
      return <Pause className={config.icon} />;
    }

    return <Play className={config.icon} />;
  };

  // 获取按钮标题
  const getButtonTitle = () => {
    if (!isSupported) return '浏览器不支持语音播放';
    if (playState.error) return `错误: ${playState.error}`;
    if (playState.isLoading) return '加载中...';
    if (playState.isPlaying && !playState.isPaused) return '暂停播放';
    if (playState.isPaused) return '继续播放';
    return '播放语音';
  };

  // 按钮状态样式
  const getButtonStyle = () => {
    const baseStyle = {
      transition: 'all 0.2s ease',
      cursor: playState.isLoading ? 'not-allowed' : 'pointer',
      opacity: playState.isLoading ? 0.6 : 1
    };

    if (!isSupported || playState.error) {
      return {
        ...baseStyle,
        backgroundColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
        color: isDarkMode ? '#A0937D' : '#8B4513',
        cursor: 'not-allowed',
        opacity: 0.5
      };
    }

    if (playState.isPlaying && !playState.isPaused) {
      return {
        ...baseStyle,
        backgroundColor: isDarkMode ? '#D2691E' : '#166534',
        color: '#FEFCF5'
      };
    }

    return {
      ...baseStyle,
      backgroundColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
      color: isDarkMode ? '#C4B59A' : '#8B4513'
    };
  };

  const handleMouseEnter = (e) => {
    if (!playState.isLoading && isSupported && !playState.error) {
      e.target.style.backgroundColor = isDarkMode ? '#D2691E' : '#166534';
      e.target.style.color = '#FEFCF5';
    }
  };

  const handleMouseLeave = (e) => {
    if (!playState.isLoading && !(playState.isPlaying && !playState.isPaused)) {
      e.target.style.backgroundColor = isDarkMode ? '#4A3F35' : '#E6D7B8';
      e.target.style.color = isDarkMode ? '#C4B59A' : '#8B4513';
    }
  };

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      {/* 主播放按钮 */}
      <button
        onClick={handlePlayPause}
        disabled={playState.isLoading || !isSupported}
        className={`voice-play-btn ${config.button} ${
          !isSupported || playState.error
            ? 'unsupported'
            : playState.isPlaying && !playState.isPaused
            ? 'playing'
            : 'default'
        }`}
        title={getButtonTitle()}
      >
        {getCurrentIcon()}
      </button>

      {/* 停止按钮（仅在播放时显示） */}
      {(playState.isPlaying || playState.isPaused) && (
        <button
          onClick={handleStop}
          className={`stop-btn ${config.button}`}
          title="停止播放"
        >
          <Square className={config.icon} />
        </button>
      )}

      {/* 错误提示（可选） */}
      {playState.error && size !== 'small' && (
        <span 
          className="text-xs ml-2"
          style={{ color: isDarkMode ? '#D2691E' : '#B91C1C' }}
          title={playState.error}
        >
          ⚠️
        </span>
      )}
    </div>
  );
};

export default VoicePlayButton;