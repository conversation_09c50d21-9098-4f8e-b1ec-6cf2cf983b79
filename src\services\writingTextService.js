// 写作文本持久化服务
const WRITING_TEXT_KEY = 'current_writing_text';
const WRITING_SUGGESTIONS_KEY = 'current_writing_suggestions';

/**
 * 保存当前写作文本
 * @param {string} text - 要保存的文本
 */
export const saveWritingText = (text) => {
  try {
    localStorage.setItem(WRITING_TEXT_KEY, text);
  } catch (error) {
    console.error('保存写作文本失败:', error);
  }
};

/**
 * 获取保存的写作文本
 * @returns {string} 保存的文本，如果没有则返回空字符串
 */
export const getWritingText = () => {
  try {
    return localStorage.getItem(WRITING_TEXT_KEY) || '';
  } catch (error) {
    console.error('获取写作文本失败:', error);
    return '';
  }
};

/**
 * 清除保存的写作文本
 */
export const clearWritingText = () => {
  try {
    localStorage.removeItem(WRITING_TEXT_KEY);
    localStorage.removeItem(WRITING_SUGGESTIONS_KEY);
  } catch (error) {
    console.error('清除写作文本失败:', error);
  }
};

/**
 * 保存当前的建议
 * @param {Array} suggestions - 要保存的建议数组
 */
export const saveWritingSuggestions = (suggestions) => {
  try {
    localStorage.setItem(WRITING_SUGGESTIONS_KEY, JSON.stringify(suggestions));
  } catch (error) {
    console.error('保存写作建议失败:', error);
  }
};

/**
 * 获取保存的建议
 * @returns {Array} 保存的建议数组，如果没有则返回空数组
 */
export const getWritingSuggestions = () => {
  try {
    const saved = localStorage.getItem(WRITING_SUGGESTIONS_KEY);
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('获取写作建议失败:', error);
    return [];
  }
};

/**
 * 自动保存写作状态（防抖）
 */
let saveTimeout = null;
export const autoSaveWritingText = (text, suggestions = []) => {
  // 清除之前的定时器
  if (saveTimeout) {
    clearTimeout(saveTimeout);
  }
  
  // 设置新的定时器，500ms后保存
  saveTimeout = setTimeout(() => {
    saveWritingText(text);
    if (suggestions.length > 0) {
      saveWritingSuggestions(suggestions);
    }
  }, 500);
};