/**
 * AI日记服务
 * 生成符合Alex人设的每日日记内容
 */

/**
 * 生成AI日记内容
 * @param {string} apiKey - API密钥
 * @returns {Promise<Object>} - 返回包含英文和中文日记的对象
 */
export const generateDailyDiary = async (apiKey) => {
  const key = apiKey || localStorage.getItem('doubao_api_key');

  if (!key) {
    throw new Error('API密钥未配置');
  }

  const apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${key}`
  };

  // 获取当前日期信息
  const today = new Date();
  const dateStr = today.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });

  const data = {
    model: 'doubao-seed-1-6-flash-250615',
    messages: [
      {
        role: 'system',
        content: `你是Alex，植物学家和自然摄影师，在哥斯达黎加云雾森林研究。写一篇150-250词的英文日记内容，描述你的发现和感受。

重要要求：
- 直接开始描述今天的经历，不要包含日期标题或时间标记
- 内容要生动有趣，体现专业的植物学知识
- 使用第一人称，就像在写私人日记
- 可以包含发现的具体植物种类、观察细节、个人感受等
- 语言要自然流畅，体现对自然的热爱和专业素养
- 将内容分成2-3个自然段落，每段之间用空行分隔，让阅读更舒适

输出格式：
请严格按照以下格式提供内容，不要添加任何其他说明：
[英文日记内容，分段显示]
---
[中文翻译，对应分段显示]`
      },
      {
        role: 'user',
        content: `请写一篇关于今天在云雾森林中的发现和体验的日记内容。记住：直接开始描述经历，不要写日期。`
      }
    ],
    temperature: 0.8,
    max_tokens: 800,
    thinking: { type: "disabled" }
  };

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const result = await response.json();
    const content = result.choices[0].message.content;

    console.log('AI原始响应:', content);
    console.log('响应长度:', content.length);
    console.log('响应前100字符:', content.substring(0, 100));

    // 解析英文和中文内容
    const parts = content.split('---');
    const english = (parts[0] || '').trim().replace('[英文日记内容]', '').trim();
    const chinese = (parts[1] || '').trim().replace('[中文翻译]', '').trim() || '翻译不可用';

    const chineseLog = chinese ? chinese.substring(0, 50) + '...' : '翻译不可用';

    console.log('📝 最终返回的日记对象:', {
      english: english.substring(0, 50) + '...',
      chinese: chineseLog,
      date: today.toISOString(),
      dateDisplay: dateStr
    });

    return {
      english,
      chinese,
      date: today.toISOString(),
      dateDisplay: dateStr
    };
  } catch (error) {
    console.error('生成日记失败:', error);
    return generateFallbackDiary(dateStr);
  }
};



/**
 * 生成备用日记内容（当API调用失败时使用）
 * @param {string} dateStr - 日期字符串
 * @returns {Object} - 备用日记对象
 */
const generateFallbackDiary = (dateStr) => {
  const fallbackDiaries = [
    {
      english: "What an incredible morning in the cloud forest! 🌿 I discovered a rare orchid species (Epidendrum radicans) growing on a moss-covered branch about 15 meters up. The way the morning mist danced around its delicate purple petals was absolutely magical - I managed to capture some stunning shots with my macro lens.\n\nThe humidity here creates such a unique ecosystem; every surface is carpeted with bryophytes and epiphytes. Later, I documented three different species of bromeliads, each hosting its own tiny ecosystem of insects and amphibians. Nature never ceases to amaze me with its intricate connections! 📸🌺",
      chinese: "云雾森林的早晨真是不可思议！🌿 我在约15米高的覆满苔藓的树枝上发现了一种稀有的兰花（Epidendrum radicans）。晨雾在它娇嫩的紫色花瓣周围舞动，简直是魔幻般的景象——我用微距镜头拍下了一些绝美的照片。\n\n这里的湿度创造了如此独特的生态系统；每个表面都覆盖着苔藓植物和附生植物。后来，我记录了三种不同的凤梨科植物，每一种都寄宿着自己的昆虫和两栖动物的微型生态系统。大自然错综复杂的联系总是让我惊叹不已！📸🌺"
    },
    {
      english: "Today I spent hours observing the fascinating relationship between hummingbirds and Heliconia flowers! 🐦 The way these tiny jeweled creatures navigate through the dense understory is pure poetry in motion. I photographed at least five different hummingbird species, including the stunning Green Hermit with its curved bill perfectly adapted for the Heliconia's tubular flowers.\n\nThe forest is alive with sounds - from the haunting calls of howler monkeys to the gentle dripping of condensed mist. Every step reveals new wonders: a delicate fern unfurling its fiddlehead, colorful fungi sprouting from decaying logs, and countless insects I've never seen before. This place is a living laboratory! 🔬🌺",
      chinese: "今天我花了好几个小时观察蜂鸟和赫蕉花之间迷人的关系！🐦 这些微小的宝石般生物在茂密的林下穿梭的方式，简直是动态的诗篇。我拍摄了至少五种不同的蜂鸟，包括令人惊叹的绿色隐士，它的弯曲鸟喙完美地适应了赫蕉的管状花朵。\n\n森林里充满了各种声音——从吼猴令人难忘的叫声到凝结的薄雾轻轻滴落的声音。每一步都有新的发现：一棵娇嫩的蕨类植物展开它的嫩芽，五彩缤纷的真菌从腐烂的圆木上长出来，还有无数我从未见过的昆虫。这个地方就是一个活生生的实验室！🔬🌺"
    }
  ];

  const randomDiary = fallbackDiaries[Math.floor(Math.random() * fallbackDiaries.length)];

  return {
    ...randomDiary,
    date: new Date().toISOString(),
    dateDisplay: dateStr,
    isFallback: true
  };
};

/**
 * 保存日记到本地存储
 * @param {Object} diary - 日记对象
 */
export const saveDiary = (diary) => {
  try {
    console.log('💾 开始保存日记:', diary);
    const diaries = getDiaries();
    const newDiary = {
      id: Date.now(),
      ...diary,
      savedAt: new Date().toISOString()
    };
    console.log('📝 创建新日记对象:', newDiary);

    // 检查今天是否已有日记
    const today = new Date().toDateString();
    const existingIndex = diaries.findIndex(d =>
      new Date(d.date).toDateString() === today
    );

    if (existingIndex >= 0) {
      // 更新今天的日记
      diaries[existingIndex] = newDiary;
    } else {
      // 添加新日记
      diaries.unshift(newDiary);
    }

    // 限制保存的日记数量（保留最近30天）
    if (diaries.length > 30) {
      diaries.splice(30);
    }

    localStorage.setItem('alex_diaries', JSON.stringify(diaries));
    console.log('✅ 日记保存成功，返回:', newDiary);
    return newDiary;
  } catch (error) {
    console.error('保存日记失败:', error);
    throw error;
  }
};

/**
 * 获取所有日记
 * @returns {Array} 日记数组
 */
export const getDiaries = () => {
  try {
    const diaries = localStorage.getItem('alex_diaries');
    return diaries ? JSON.parse(diaries) : [];
  } catch (error) {
    console.error('获取日记失败:', error);
    return [];
  }
};

/**
 * 获取今天的日记
 * @returns {Object|null} 今天的日记或null
 */
export const getTodayDiary = () => {
  try {
    const diaries = getDiaries();
    const today = new Date().toDateString();
    return diaries.find(diary =>
      new Date(diary.date).toDateString() === today
    ) || null;
  } catch (error) {
    console.error('获取今天日记失败:', error);
    return null;
  }
};

/**
 * 删除指定日记
 * @param {number} diaryId - 日记ID
 */
export const deleteDiary = (diaryId) => {
  try {
    const diaries = getDiaries();
    const filteredDiaries = diaries.filter(diary => diary.id !== diaryId);
    localStorage.setItem('alex_diaries', JSON.stringify(filteredDiaries));
  } catch (error) {
    console.error('删除日记失败:', error);
    throw error;
  }
};