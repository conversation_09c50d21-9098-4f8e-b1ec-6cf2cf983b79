const HISTORY_KEY = 'dictionary_search_history';
const MAX_HISTORY_ITEMS = 20; // 设置历史记录最大条目数

/**
 * 从 localStorage 获取搜索历史记录
 * @returns {string[]}
 */
export const getSearchHistory = () => {
  try {
    const history = localStorage.getItem(HISTORY_KEY);
    return history ? JSON.parse(history) : [];
  } catch (error) {
    console.error('获取搜索历史失败:', error);
    return [];
  }
};

/**
 * 将新词条添加到搜索历史
 * @param {string} term - 要添加的搜索词
 */
export const addSearchToHistory = (term) => {
  if (!term || typeof term !== 'string' || term.trim() === '') return;
  
  const lowerCaseTerm = term.trim().toLowerCase();

  try {
    let history = getSearchHistory();
    // 移除已存在的相同词条，以避免重复并将其置顶
    history = history.filter(item => item.toLowerCase() !== lowerCaseTerm);
    
    // 将新词条添加到数组开头
    history.unshift(term.trim());
    
    // 限制历史记录的长度
    if (history.length > MAX_HISTORY_ITEMS) {
      history = history.slice(0, MAX_HISTORY_ITEMS);
    }
    
    localStorage.setItem(HISTORY_KEY, JSON.stringify(history));
  } catch (error) {
    console.error('保存搜索历史失败:', error);
  }
};

/**
 * 清空搜索历史
 */
export const clearSearchHistory = () => {
  try {
    localStorage.removeItem(HISTORY_KEY);
  } catch (error) {
    console.error('清空搜索历史失败:', error);
  }
};

// --- AI 分析历史记录 ---

const AI_ANALYSIS_HISTORY_KEY = 'ai_analysis_history';

/**
 * 获取AI分析历史记录
 * @returns {Array<object>}
 */
export const getAnalysisHistory = () => {
  try {
    const history = localStorage.getItem(AI_ANALYSIS_HISTORY_KEY);
    return history ? JSON.parse(history) : [];
  } catch (error) {
    console.error('获取AI分析历史失败:', error);
    return [];
  }
};

/**
 * 保存AI分析记录
 * @param {string} text - 原始文本
 * @param {string} rawAnalysis - AI分析的原始结果
 * @param {object} analysis - AI分析的结构化结果
 */
export const saveAnalysisToHistory = (record) => {
  try {
    const { text, rawAnalysis, analysis } = record;
    const history = getAnalysisHistory();
    const newRecord = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      text,
      rawAnalysis,
      analysis,
    };
    
    // 保证记录唯一性，如果需要的话
    const updatedHistory = [newRecord, ...history];
    
    // 可以在这里添加历史记录数量限制
    // if (updatedHistory.length > 50) {
    //   updatedHistory.pop();
    // }
    
    localStorage.setItem(AI_ANALYSIS_HISTORY_KEY, JSON.stringify(updatedHistory));
  } catch (error) {
    console.error('保存AI分析历史失败:', error);
  }
};

/**
 * 删除单条AI分析历史记录
 * @param {number} recordId - 要删除的记录ID
 * @returns {Array<object>} 更新后的历史记录
 */
export const deleteHistoryRecord = (recordId) => {
  try {
    let history = getAnalysisHistory();
    const updatedHistory = history.filter(record => record.id !== recordId);
    localStorage.setItem(AI_ANALYSIS_HISTORY_KEY, JSON.stringify(updatedHistory));
    return updatedHistory;
  } catch (error) {
    console.error('删除AI分析历史失败:', error);
    return getAnalysisHistory(); // 返回原始历史记录
  }
};

/**
 * 清空所有AI分析历史记录
 */
export const clearAllHistory = () => {
  try {
    localStorage.removeItem(AI_ANALYSIS_HISTORY_KEY);
  } catch (error) {
    console.error('清空AI分析历史失败:', error);
  }
};