import { useState, useRef, useEffect, useLayoutEffect } from 'react';
import * as chatService from '../services/chatService';
import { saveChatSession, startNewChatSession, getCurrentSessionId, cleanupDuplicateHistory } from '../services/chatHistoryService';
import { ttsService } from '../services/ttsService';

const initialMessage = {
    id: 1,
    type: 'ai',
    content: "Hey there! 👋 I'm <PERSON>, a botanist and nature photographer currently exploring the cloud forests of Costa Rica! 🌿📸 I just finished cataloging some incredible orchid species this morning. I love chatting about nature, photography, travel, or really anything that sparks curiosity! What's growing in your world today?",
    translation: "嘿！👋 我是Alex，一位植物学家和自然摄影师，目前正在探索哥斯达黎加的云雾森林！🌿📸 我刚刚完成了今天早上一些令人难以置信的兰花物种编目工作。我喜欢聊自然、摄影、旅行，或者任何能激发好奇心的话题！你的世界里在发生什么有趣的事情呢？",
    timestamp: new Date()
};

export function parseAIResponse(response) {
    console.log('🔍 parseAIResponse 输入:', response);
    console.log('🔍 响应类型:', typeof response);
    console.log('🔍 响应长度:', response?.length);
    
    // 确保response是字符串
    const responseStr = typeof response === 'string' ? response : String(response);
    
    // 使用 "---" 分隔符来分离英文和中文内容
    const separator = '---';
    const parts = responseStr.split(separator);
    
    console.log('🔍 分割后的部分数量:', parts.length);
    
    let englishContent = '';
    let chineseContent = '';
    
    if (parts.length >= 2) {
        // 有分隔符，第一部分是英文，第二部分是中文
        englishContent = parts[0].trim();
        chineseContent = parts[1].trim();
        console.log('🔍 提取的英文内容:', englishContent);
        console.log('🔍 提取的中文内容:', chineseContent);
    } else {
        // 没有分隔符，可能是纯英文回复或者旧格式
        const cleanResponse = responseStr.trim();
        
        // 检查是否还有旧的标签格式（向后兼容）
        if (cleanResponse.includes('[ENGLISH]') || cleanResponse.includes('[CHINESE]')) {
            console.log('🔍 检测到旧标签格式，使用兼容解析');
            return parseOldTagFormat(cleanResponse);
        }
        
        // 纯英文回复
        console.log('🔍 检测到纯英文回复');
        englishContent = cleanResponse;
        chineseContent = '翻译暂不可用';
    }
    
    // 构建结果
    const result = {
        english: englishContent || responseStr.trim(),
        chinese: chineseContent || '翻译暂不可用'
    };
    
    console.log('✅ parseAIResponse 最终输出:', result);
    return result;
}

// 向后兼容的旧标签格式解析函数
function parseOldTagFormat(responseStr) {
    console.log('🔄 使用旧标签格式解析');
    
    let englishContent = '';
    let chineseContent = '';
    
    // 查找英文内容
    const englishStartTag = '[ENGLISH]';
    const englishEndTag = '[/ENGLISH]';
    const englishStartIndex = responseStr.indexOf(englishStartTag);
    const englishEndIndex = responseStr.indexOf(englishEndTag);
    
    if (englishStartIndex !== -1 && englishEndIndex !== -1 && englishEndIndex > englishStartIndex) {
        englishContent = responseStr.substring(
            englishStartIndex + englishStartTag.length,
            englishEndIndex
        ).trim();
    }
    
    // 查找中文内容
    const chineseStartTag = '[CHINESE]';
    const chineseEndTag = '[/CHINESE]';
    const chineseStartIndex = responseStr.indexOf(chineseStartTag);
    const chineseEndIndex = responseStr.indexOf(chineseEndTag);
    
    if (chineseStartIndex !== -1 && chineseEndIndex !== -1 && chineseEndIndex > chineseStartIndex) {
        chineseContent = responseStr.substring(
            chineseStartIndex + chineseStartTag.length,
            chineseEndIndex
        ).trim();
    }
    
    return {
        english: englishContent || responseStr.trim(),
        chinese: chineseContent || '翻译暂不可用'
    };
}

export function useChat(autoPlayTTS, sharedWritingContext) {
    const [messages, setMessages] = useState(() => {
        try {
            const savedMessages = localStorage.getItem('current_chat_messages');
            if (savedMessages) {
                const parsed = JSON.parse(savedMessages);
                if (Array.isArray(parsed) && parsed.length > 0) {
                    // 检查是否有旧格式的消息（包含标签）
                    const hasOldFormat = parsed.some(msg => 
                        msg.content && (msg.content.includes('[ENGLISH]') || msg.content.includes('[CHINESE]'))
                    );
                    
                    if (hasOldFormat) {
                        console.log('🧹 检测到旧格式消息，清理localStorage');
                        localStorage.removeItem('current_chat_messages');
                        return [initialMessage];
                    }
                    
                    return parsed.map(msg => ({ ...msg, timestamp: new Date(msg.timestamp) }));
                }
            }
        } catch (error) {
            console.error('恢复对话记录失败:', error);
        }
        return [initialMessage];
    });

    const [isLoading, setIsLoading] = useState(false);
    const messagesEndRef = useRef(null);
    const messagesRef = useRef(messages);

    useEffect(() => {
        messagesRef.current = messages;
    }, [messages]);

    const scrollToBottom = (behavior) => {
        messagesEndRef.current?.scrollIntoView({ behavior });
    };

    // 新消息平滑滚动
    useEffect(() => {
        // 避免在初始消息时滚动
        if (messages.length > 1) { 
            scrollToBottom('smooth');
        }
        localStorage.setItem('current_chat_messages', JSON.stringify(messages));
    }, [messages]);

    // 初始加载时即时滚动
    useLayoutEffect(() => {
        scrollToBottom('auto');
    }, []); // 仅在挂载时运行

    useEffect(() => {
        cleanupDuplicateHistory();
        
        // 如果没有当前会话ID，开始新会话
        if (!getCurrentSessionId()) {
            startNewChatSession();
        }
        
        return () => {
            // 组件卸载时保存当前会话
            if (messagesRef.current.length > 1) {
                saveChatSession(messagesRef.current);
            }
        };
    }, []);

    const playTTS = async (text) => {
        if (!text || !text.trim() || !autoPlayTTS) return;
        try {
            await ttsService.speak(text, { lang: 'en-US', rate: 0.9, pitch: 1.0, volume: 1.0 });
        } catch (error) {
            console.error('TTS自动播放失败:', error);
        }
    };

    const handleSendMessage = async (inputText) => {
        if (!inputText.trim() || isLoading) return;

        const userMessage = { id: Date.now(), type: 'user', content: inputText.trim(), timestamp: new Date() };
        const updatedMessages = [...messages, userMessage];
        setMessages(updatedMessages);
        setIsLoading(true);

        try {
            const aiResponse = await chatService.getChatResponse(inputText.trim(), messages, sharedWritingContext);
            console.log('🤖 AI原始响应:', aiResponse);
            const parsed = parseAIResponse(aiResponse);
            console.log('📝 解析后的内容:', { english: parsed.english, chinese: parsed.chinese });
            const aiMessage = { id: Date.now() + 1, type: 'ai', content: parsed.english, translation: parsed.chinese, timestamp: new Date() };
            const finalMessages = [...updatedMessages, aiMessage];
            setMessages(finalMessages);
            
            // 保存完整的对话到当前会话
            saveChatSession(finalMessages);
            
            playTTS(parsed.english);
        } catch (error) {
            console.error('Chat error:', error);
            const errorMessage = { id: Date.now() + 1, type: 'ai', content: "I'm sorry, I'm having trouble connecting right now. Could you try again?", translation: "抱歉，我现在连接有点问题。你能再试一次吗？", timestamp: new Date() };
            const finalMessages = [...updatedMessages, errorMessage];
            setMessages(finalMessages);
            
            // 保存包含错误消息的对话
            saveChatSession(finalMessages);
            
            playTTS(errorMessage.content);
        } finally {
            setIsLoading(false);
        }
    };

    const handleNewConversation = async () => {
        if (isLoading) return;
        
        // 保存当前会话（如果有消息的话）
        if (messages.length > 1) {
            saveChatSession(messages);
        }
        
        // 开始新的会话
        startNewChatSession();
        
        setIsLoading(true);
        
        // 强制清理可能的旧格式数据
        localStorage.removeItem('current_chat_messages');
        localStorage.removeItem('current_chat_suggestions');
        localStorage.removeItem('shared_writing_context');
        
        // 清理可能的缓存消息
        setMessages([]);
        console.log('🧹 已清理所有聊天相关的localStorage数据和缓存消息');
        
        try {
            const greetingResponse = await chatService.generateNewGreeting();
            const parsedGreeting = parseAIResponse(greetingResponse);
            const newGreeting = { id: Date.now(), type: 'ai', content: parsedGreeting.english, translation: parsedGreeting.chinese, timestamp: new Date() };
            console.log('🎉 新对话消息:', newGreeting);
            setMessages([newGreeting]);
            playTTS(parsedGreeting.english);
        } catch (error) {
            console.error('生成新开场白失败:', error);
            setMessages([initialMessage]); // Fallback to initial message
            playTTS(initialMessage.content);
        } finally {
            setIsLoading(false);
        }
    };

    return { messages, setMessages, isLoading, handleSendMessage, handleNewConversation, messagesEndRef };
}