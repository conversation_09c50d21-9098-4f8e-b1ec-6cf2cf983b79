// API 配置文件
// 在生产环境中，请使用环境变量来存储敏感信息

// 获取API密钥的函数
const getAPIKey = () => {
  // 优先从localStorage获取
  const localKey = typeof window !== 'undefined' ? localStorage.getItem('doubao_api_key') : null;
  if (localKey) return localKey;

  // 其次从环境变量获取
  const envKey = process.env.REACT_APP_DOUBAO_API_KEY;
  if (envKey && envKey !== 'YOUR_API_KEY_HERE') return envKey;

  return '';
};

export const API_CONFIG = {
  // 豆包 API 配置
  doubao: {
    baseURL: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
    model: 'doubao-seed-1-6-flash-250615',
    // 动态获取API密钥
    get apiKey() {
      return getAPIKey();
    }
  },
  
  // 请求配置
  request: {
    timeout: 30000, // 30秒超时
    maxRetries: 3,  // 最大重试次数
    temperature: 0.3, // AI 创造性参数
    maxTokens: 2000   // 最大返回token数
  }
};

// 检查API密钥是否已配置
export const isAPIKeyConfigured = () => {
  const apiKey = getAPIKey();
  return apiKey && apiKey.trim().length > 0;
};

// 获取API配置状态
export const getAPIStatus = () => {
  return {
    configured: isAPIKeyConfigured(),
    model: API_CONFIG.doubao.model,
    baseURL: API_CONFIG.doubao.baseURL
  };
};
