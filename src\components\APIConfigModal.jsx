import React, { useState, useEffect } from 'react';
import { X, Key, ExternalLink, AlertCircle, CheckCircle } from 'lucide-react';
import { API_CONFIG, isAPIKeyConfigured, getAPIStatus } from '../config/apiConfig';

const APIConfigModal = ({ isOpen, onClose, onSave }) => {
  const [apiKey, setApiKey] = useState('');
  const [isValid, setIsValid] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState(null);

  useEffect(() => {
    if (isOpen) {
      // 加载当前配置的API密钥（如果有的话）
      const currentKey = localStorage.getItem('siliconflow_api_key') || '';
      setApiKey(currentKey);
      setIsValid(currentKey.length > 0);
      setTestResult(null);
    }
  }, [isOpen]);

  const handleApiKeyChange = (e) => {
    const value = e.target.value.trim();
    setApiKey(value);
    setIsValid(value.length > 0);
    setTestResult(null);
  };

  const testAPIKey = async () => {
    if (!apiKey) return;

    setIsTesting(true);
    setTestResult(null);

    try {
      const response = await fetch(API_CONFIG.siliconFlow.baseURL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: API_CONFIG.siliconFlow.model,
          messages: [
            {
              role: 'user',
              content: 'Hello, this is a test message.'
            }
          ],
          max_tokens: 10
        })
      });

      if (response.ok) {
        setTestResult({ success: true, message: 'API密钥验证成功！' });
      } else {
        const errorData = await response.json().catch(() => ({}));
        setTestResult({ 
          success: false, 
          message: `验证失败: ${response.status} ${errorData.error?.message || response.statusText}` 
        });
      }
    } catch (error) {
      setTestResult({ 
        success: false, 
        message: `连接失败: ${error.message}` 
      });
    } finally {
      setIsTesting(false);
    }
  };

  const handleSave = () => {
    if (apiKey) {
      // 保存到localStorage
      localStorage.setItem('siliconflow_api_key', apiKey);
      // 更新配置
      API_CONFIG.siliconFlow.apiKey = apiKey;
    } else {
      // 清除配置
      localStorage.removeItem('siliconflow_api_key');
      API_CONFIG.siliconFlow.apiKey = '';
    }
    
    onSave && onSave(apiKey);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <Key className="w-5 h-5 text-primary-600" />
            API 配置
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-medium text-blue-900 mb-2">获取 SiliconFlow API 密钥</h3>
            <p className="text-sm text-blue-700 mb-3">
              要使用AI分析功能，你需要一个SiliconFlow API密钥。
            </p>
            <a
              href="https://cloud.siliconflow.cn/"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              <ExternalLink className="w-4 h-4" />
              访问 SiliconFlow 获取API密钥
            </a>
          </div>

          <div>
            <label htmlFor="apiKey" className="block text-sm font-medium text-gray-700 mb-2">
              API 密钥
            </label>
            <input
              id="apiKey"
              type="password"
              value={apiKey}
              onChange={handleApiKeyChange}
              placeholder="请输入你的 SiliconFlow API 密钥"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          {testResult && (
            <div className={`flex items-start gap-2 p-3 rounded-lg ${
              testResult.success 
                ? 'bg-green-50 border border-green-200' 
                : 'bg-red-50 border border-red-200'
            }`}>
              {testResult.success ? (
                <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
              ) : (
                <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
              )}
              <p className={`text-sm ${
                testResult.success ? 'text-green-700' : 'text-red-700'
              }`}>
                {testResult.message}
              </p>
            </div>
          )}

          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">使用说明</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• API密钥将安全地存储在你的浏览器本地</li>
              <li>• 不会上传到任何服务器</li>
              <li>• 用于调用Qwen3-32B模型进行文本分析</li>
              <li>• 如果不配置，将使用演示数据</li>
            </ul>
          </div>
        </div>

        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={testAPIKey}
            disabled={!isValid || isTesting}
            className="px-4 py-2 text-sm font-medium text-primary-600 bg-white border border-primary-600 rounded-lg hover:bg-primary-50 disabled:bg-gray-100 disabled:text-gray-400 disabled:border-gray-300 disabled:cursor-not-allowed transition-colors"
          >
            {isTesting ? '测试中...' : '测试连接'}
          </button>
          
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 transition-colors"
            >
              保存
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default APIConfigModal;
