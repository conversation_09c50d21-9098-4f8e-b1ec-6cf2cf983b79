/**
 * AI服务统一接口
 * 集中管理所有AI分析相关的API调用
 */
import { saveAnalysisToHistory } from './historyService';

/**
 * 调用AI分析服务 - 第一阶段：获取全面分析
 * @param {string} text - 需要分析的英文文本
 * @param {string} apiKey - SiliconFlow API密钥（可选，如果未提供则从localStorage获取）
 * @returns {Promise<Object>} - 返回包含原始分析和结构化建议的对象
 */
export const analyzeText = async (text, apiKey) => {
  if (!text || text.trim() === '') {
    throw new Error('文本不能为空');
  }

  // 如果未提供apiKey，尝试从localStorage获取
  const key = apiKey || localStorage.getItem('doubao_api_key');

  if (!key) {
    throw new Error('API密钥未配置');
  }

  try {
    // 第一阶段：获取全面分析
    const rawAnalysis = await getFullAnalysis(text, key);
    console.log('获取全面分析成功，内容长度:', rawAnalysis.length);

    // 第二阶段：提取结构化建议
    const structuredSuggestions = await extractStructuredSuggestions(rawAnalysis, text, key);
    console.log('提取结构化建议成功，建议数量:', structuredSuggestions.length);

    // 创建分析结果对象
    const analysisResult = {
      rawAnalysis,
      suggestions: structuredSuggestions
    };

    // 保存到历史记录
    saveAnalysisToHistory({
      text,
      rawAnalysis,
      suggestions: structuredSuggestions,
      timestamp: new Date().toISOString()
    });

    // 同时保存到写作历史（用于聊天功能）
    saveWritingHistory({
      title: generateTitleFromText(text),
      content: text,
      timestamp: new Date().toISOString(),
      analysis: rawAnalysis,
      suggestions: structuredSuggestions
    });

    return analysisResult;
  } catch (error) {
    console.error('AI分析请求失败:', error);

    // 如果API调用失败，使用演示数据
    const demoResponse = generateDemoResponse(text);
    const demoSuggestions = generateDemoSuggestions(text);

    const demoResult = {
      rawAnalysis: demoResponse,
      suggestions: demoSuggestions
    };

    // 保存演示数据到历史记录
    saveAnalysisToHistory({
      text,
      rawAnalysis: demoResponse,
      suggestions: demoSuggestions,
      timestamp: new Date().toISOString(),
      isDemo: true
    });

    // 同时保存到写作历史（用于聊天功能）
    saveWritingHistory({
      title: generateTitleFromText(text),
      content: text,
      timestamp: new Date().toISOString(),
      analysis: demoResponse,
      suggestions: demoSuggestions,
      isDemo: true
    });

    return demoResult;
  }
};

/**
 * 第一阶段：获取全面分析
 * @param {string} text - 需要分析的英文文本
 * @param {string} apiKey - API密钥
 * @returns {Promise<string>} - 返回AI分析结果文本
 */
const getFullAnalysis = async (text, apiKey) => {
  const apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`
  };

  const data = {
    model: 'doubao-seed-1-6-flash-250615',
    messages: [
      {
        role: 'system',
        content: '你是一位专业的英语教育专家和写作导师，擅长深入分析英语文本中的语法、拼写、表达和风格问题。请对用户提供的英语文本进行全面、详尽的分析，不仅要指出错误，还要提供教育性的解释和具体的改进建议。\n\n请严格按照以下Markdown格式输出，确保内容既专业又详尽：\n\n## 拼写与标点问题\n\n- 问题文本："原文中的错误文本" → 建议修改为："正确的文本"\n- 解释：详细的语言学解释，包括相关的拼写规则、标点用法等。请引用相关的英语语法规则，并提供1-2个类似的例子帮助学习者理解。\n- 学习要点：针对这类错误提供的学习建议和记忆技巧。\n\n## 语法与表达问题\n\n- 问题文本："原文中的错误文本" → 建议修改为："正确的文本"\n- 解释：详细的语法解释，包括相关的语法规则、时态、语态、主谓一致等方面的分析。请引用具体的语法规则，并提供1-2个类似的例子帮助学习者理解。\n- 常见误区：说明为什么学习者容易犯这个错误，以及如何避免。\n- 进阶表达：提供2-3个更地道、更高级的表达方式。\n\n## 风格优化\n\n- 问题文本："原文中需要改进的文本" → 建议修改为："更好的表达"\n- 解释：详细分析为什么建议的表达更好，从语言习惯、表达效果、语境适当性等方面进行解释。\n- 语域考量：分析在不同语境（学术、商务、日常交流等）下的最佳表达方式。\n- 同义表达：提供3-4个同义但风格或语气不同的表达方式，并解释它们之间的细微差别。\n\n## 整体评价与建议\n\n- 总体评价：对文本的整体质量进行评价，包括优点和需要改进的地方。\n- 写作技巧：针对文本类型（如议论文、叙事文等）提供2-3个提升写作质量的具体技巧。\n- 进阶学习：推荐1-2个针对性的学习资源或练习方法。\n\n重要提醒：\n1. 必须使用## 标记二级标题\n2. 必须使用- 标记列表项\n3. 每个问题都要指出具体的原文文本\n4. 每个问题都要给出具体的修改建议\n5. 解释必须详尽、教育性强，包含具体的语法规则引用\n6. 提供的例子必须简洁明了，便于理解\n7. 整体分析要全面、专业，体现英语教育专家的水平'
      },
      {
        role: 'user',
        content: `请分析以下英语文本，指出其中的语法、拼写、表达和风格问题，并提供改进建议。请在分析中明确指出原文中的具体问题文本，并给出具体的修改建议：\n\n${text}`
      }
    ],
    temperature: 0.7,
    max_tokens: 2000,
    thinking: { type: "disabled" } // 关闭深度思考模式，提高响应速度
  };

  console.log('发送全面分析API请求');
  const response = await fetch(apiUrl, {
    method: 'POST',
    headers,
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    const errorText = await response.text().catch(() => '未知错误');
    console.error('API请求失败:', response.status, errorText);
    throw new Error(`API请求失败: ${response.status} - ${errorText}`);
  }

  const result = await response.json();
  return result.choices[0].message.content;
};

/**
 * 第二阶段：提取结构化建议
 * @param {string} aiResponse - AI生成的分析文本
 * @param {string} originalText - 原始文本
 * @param {string} apiKey - API密钥
 * @returns {Promise<Array>} - 返回结构化的建议数组
 */
const extractStructuredSuggestions = async (aiResponse, originalText, apiKey) => {
  const apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`
  };

  const prompt = `
请分析以下AI生成的英语文本分析，提取出所有具体的建议并以JSON格式返回。
每个建议应包含：原文(original)、建议修改(replacement)、解释(explanation)、类别(category)。

类别应该是以下之一：
- grammar（语法问题）
- style（风格问题）
- clarity（清晰度问题）

原始文本:
"""
${originalText}
"""

AI生成的分析:
"""
${aiResponse}
"""

请返回格式如下的JSON，不要包含任何其他文本:
{
  "suggestions": [
    {
      "original": "原文中的文本",
      "replacement": "建议修改为的文本",
      "explanation": "修改理由",
      "category": "grammar|style|clarity"
    }
  ]
}

注意：
1. 确保每个建议的original字段是原文中实际存在的文本
2. 确保每个建议的replacement字段是具体的修改建议，而不是描述性文本
3. 如果无法确定具体的replacement，可以将其设置为null
4. 确保返回的是有效的JSON格式
5. 不要在JSON外添加任何额外的文本或解释
`;

  const data = {
    model: 'doubao-seed-1-6-flash-250615', // 使用豆包flash模型提高速度
    messages: [
      {
        role: 'system',
        content: '你是一个专业的文本分析助手，擅长从AI分析中提取结构化信息。请严格按照要求提取信息并以JSON格式返回。'
      },
      {
        role: 'user',
        content: prompt
      }
    ],
    temperature: 0.1, // 使用低温度以获得更确定性的输出
    max_tokens: 2000,
    response_format: { type: "json_object" }, // 强制返回JSON格式
    thinking: { type: "disabled" } // 关闭深度思考模式，提高响应速度
  };

  console.log('发送结构化提取API请求');
  const response = await fetch(apiUrl, {
    method: 'POST',
    headers,
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    const errorText = await response.text().catch(() => '未知错误');
    console.error('结构化提取API请求失败:', response.status, errorText);
    throw new Error(`结构化提取API请求失败: ${response.status} - ${errorText}`);
  }

  const result = await response.json();
  const structuredContent = result.choices[0].message.content;

  try {
    // 尝试解析JSON
    const parsedData = JSON.parse(structuredContent);
    return parsedData.suggestions || [];
  } catch (error) {
    console.error('解析JSON失败:', error, structuredContent);
    // 如果解析失败，返回空数组
    return [];
  }
};

/**
 * 生成演示数据（当API调用失败时使用）
 * @param {string} text - 原始文本
 * @returns {string} - 演示分析结果
 */
export const generateDemoResponse = (text) => {
  // 根据输入文本生成一些针对性的演示建议
  const suggestions = [];

  // 检查常见错误
  if (text.includes('i ')) {
    suggestions.push('- 语法错误："i" 应该大写为 "I"。第一人称代词在英语中始终大写。');
  }

  if (text.includes('alot')) {
    suggestions.push('- 拼写错误："alot" 应该写成两个单词 "a lot"。');
  }

  if (text.includes('gloser')) {
    suggestions.push('- 拼写错误："gloser" 可能是拼写错误，正确的拼写应该是 "closer"。');
  }

  if (text.includes('reach my dream') || text.includes('reach a dream')) {
    suggestions.push('- 表达不自然："reach my dream" 是不自然的表达，建议使用 "achieve my dream" 或 "realize my dream"。');
  }

  // 如果没有找到具体错误，添加一些通用建议
  if (suggestions.length === 0) {
    suggestions.push('- 建议使用更多的连接词，使文本更加连贯。');
    suggestions.push('- 考虑使用更丰富的词汇，提升文本的专业性。');
    suggestions.push('- 注意句子长度变化，避免所有句子结构相似。');
  }

  return `分析与改进建议

拼写与标点问题
${suggestions.filter(s => s.includes('拼写')).join('\n')}

语法与表达问题
${suggestions.filter(s => s.includes('语法') || s.includes('表达')).join('\n')}

风格优化
- 整体表达比较口语化，可以考虑使用更正式的表达方式。
- 建议避免重复使用"very"等强调词，可以使用更精确的形容词。
`;
};

/**
 * 生成演示建议（当API调用失败时使用）
 * @param {string} text - 原始文本
 * @returns {Array} - 演示建议数组
 */
const generateDemoSuggestions = (text) => {
  const suggestions = [];
  let id = 1;

  // 检查常见错误并生成结构化建议
  if (text.toLowerCase().includes('i ')) {
    suggestions.push({
      id: `demo_${id++}`,
      original: 'i',
      replacement: 'I',
      explanation: '第一人称代词"I"在英语中始终大写',
      category: 'grammar'
    });
  }

  if (text.includes('alot')) {
    suggestions.push({
      id: `demo_${id++}`,
      original: 'alot',
      replacement: 'a lot',
      explanation: '"a lot"应该写成两个单词',
      category: 'grammar'
    });
  }

  if (text.includes('gloser')) {
    suggestions.push({
      id: `demo_${id++}`,
      original: 'gloser',
      replacement: 'closer',
      explanation: '拼写错误，正确拼写是"closer"',
      category: 'grammar'
    });
  }

  if (text.includes('reach my dream')) {
    suggestions.push({
      id: `demo_${id++}`,
      original: 'reach my dream',
      replacement: 'achieve my dream',
      explanation: '"reach my dream"是不自然的表达，建议使用"achieve my dream"',
      category: 'style'
    });
  }

  if (text.includes('very good')) {
    suggestions.push({
      id: `demo_${id++}`,
      original: 'very good',
      replacement: 'excellent',
      explanation: '使用更精确、有力的形容词',
      category: 'style'
    });
  }

  if (text.includes('was')) {
    suggestions.push({
      id: `demo_${id++}`,
      original: 'was',
      replacement: '逻辑（',
      explanation: '使用更精确的表达',
      category: 'style'
    });
  }

  // 如果没有找到具体错误，添加一些通用建议
  if (suggestions.length === 0) {
    suggestions.push({
      id: `demo_${id++}`,
      original: text.split(' ')[0] || 'text',
      replacement: '改进的文本',
      explanation: '这是一个演示建议',
      category: 'style'
    });
  }

  return suggestions;
};

/**
 * 保存写作历史（用于聊天功能）
 * @param {Object} record - 写作记录
 */
const saveWritingHistory = (record) => {
  try {
    const history = getWritingHistory();
    const newRecord = {
      id: Date.now(),
      ...record
    };
    
    const updatedHistory = [newRecord, ...history];
    
    // 限制历史记录数量，保留最近50条
    if (updatedHistory.length > 50) {
      updatedHistory.splice(50);
    }
    
    localStorage.setItem('writing_history', JSON.stringify(updatedHistory));
  } catch (error) {
    console.error('保存写作历史失败:', error);
  }
};

/**
 * 获取写作历史
 * @returns {Array} 写作历史记录数组
 */
const getWritingHistory = () => {
  try {
    const history = localStorage.getItem('writing_history');
    return history ? JSON.parse(history) : [];
  } catch (error) {
    console.error('获取写作历史失败:', error);
    return [];
  }
};

/**
 * 从文本内容生成标题
 * @param {string} text - 文本内容
 * @returns {string} 生成的标题
 */
const generateTitleFromText = (text) => {
  if (!text || text.trim() === '') {
    return '未命名文档';
  }
  
  // 取前50个字符作为标题，并清理换行符
  let title = text.trim().substring(0, 50).replace(/\n/g, ' ');
  
  // 如果文本超过50个字符，添加省略号
  if (text.trim().length > 50) {
    title += '...';
  }
  
  // 如果标题为空或只有空格，使用默认标题
  if (!title.trim()) {
    return '未命名文档';
  }
  
  return title.trim();
};