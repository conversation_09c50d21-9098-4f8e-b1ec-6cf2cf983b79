import React, { useState } from 'react';
import VoiceInputButton from './VoiceInputButton';

const VoiceInputTest = () => {
    const [text, setText] = useState('');
    const [transcript, setTranscript] = useState('');

    const handleTranscriptChange = (newTranscript) => {
        setTranscript(newTranscript);
    };

    const handleFinalTranscript = (finalTranscript) => {
        setText(prev => prev ? `${prev} ${finalTranscript}` : finalTranscript);
        setTranscript('');
    };

    return (
        <div className="p-8 max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold mb-4">语音输入测试</h2>
            
            <div className="mb-4">
                <label className="block text-sm font-medium mb-2">
                    文本内容:
                </label>
                <textarea
                    value={transcript ? `${text}${transcript}` : text}
                    onChange={(e) => setText(e.target.value)}
                    className="w-full p-3 border rounded-lg"
                    rows={6}
                    placeholder="在这里输入文本，或使用语音输入..."
                />
            </div>

            <div className="flex items-center gap-4">
                <VoiceInputButton
                    onTranscriptChange={handleTranscriptChange}
                    onFinalTranscript={handleFinalTranscript}
                    isDarkMode={false}
                />
                <span className="text-sm text-gray-600">
                    点击麦克风开始语音输入
                </span>
            </div>

            {transcript && (
                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="text-sm text-blue-600 mb-1">正在识别:</div>
                    <div className="italic">{transcript}</div>
                </div>
            )}
        </div>
    );
};

export default VoiceInputTest;