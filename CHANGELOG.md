# 更新日志

## [1.2.5] - 2025-01-08

### 🐛 Bug修复
- **修复聊天格式显示问题**
  - 添加自动检测和清理localStorage中旧格式消息的功能
  - 在useChat初始化时检测包含标签的旧消息并自动清理
  - 在新对话时强制清理所有聊天相关的localStorage数据
  - 确保AI回复只显示纯英文内容，中文翻译在hover气泡中显示
  - 添加详细的调试日志帮助问题排查

## [1.2.4] - 2025-01-08

### 🎨 UI/UX改进
- **优化AI分析按钮交互反馈**
  - 将加载动画从头部区域移动到右下角AI分析按钮内部
  - 按钮在分析状态下显示旋转动画，提供即时视觉反馈
  - 移除头部区域的"分析中..."提示，保持界面简洁
  - 根据状态动态更新按钮的title提示文本
  - 提供更直观和连贯的用户体验

## [1.2.3] - 2025-01-08

### 🎨 UI改进
- **统一滚动条样式**
  - 修复写作历史记录在暗色模式下滚动条样式不协调的问题
  - 为HistoryModal组件的滚动区域添加custom-scrollbar类
  - 确保与词典模态框的滚动条样式保持一致
  - 提供更好的暗色模式视觉体验

### 🧹 代码清理
- 删除临时调试文件，保持代码库整洁
- 更新.gitignore，防止未来意外提交临时文件

## [1.2.2] - 2025-01-08

### 🐛 Bug修复
- **修复日记页面ReferenceError错误**
  - 修复ProfileModal组件中handleChatWithDiary未定义的问题
  - 更新ProfileModal的props定义，添加onChatWithDiary参数
  - 修复ProfileModal调用时缺少onChatWithDiary属性传递
  - 修复DiarySection组件中DiaryItem的props传递问题
  - 添加详细的调试日志以便问题排查

### 🔧 技术改进
- 完善组件间的props传递链：ChatPage → ProfileModal → DiarySection → DiaryItem
- 在DiarySection中添加数据加载和渲染调试信息
- 优化错误处理和调试体验
- 确保所有回调函数正确传递

## [1.2.1] - 2025-01-08

### 🐛 Bug修复
- **修复词典和设置按钮无响应问题**
  - 修复AppContext状态管理问题，添加showApiConfig和showDictionary状态
  - 统一EditorPage和ChatPage的模态框状态管理系统
  - 修改EditorPage使用AppContext dispatch替代useModalManager
  - 修复Header组件的设置按钮回调函数
  - 导出并导入parseAIResponse函数
  - 修复正则表达式转义字符问题
  - 移除重复的DictionaryModal组件和未使用的导入
  - 添加详细的调试日志以便问题排查

### 🔧 技术改进
- 扩展AppContext初始状态和reducer函数
- 统一模态框状态管理到AppContext，避免多套系统冲突
- 优化renderClickableText函数的正则表达式
- 改进handleWordClick和handleLookupWord函数的处理逻辑
- 清理EditorPage中未使用的代码和导入

## [1.2.0] - 2025-01-08

### ✨ 新增功能
- **日记聊天功能**: 在每篇AI日记旁添加聊天按钮
  - 点击聊天按钮可将日记内容作为上下文加载到AI对话中
  - 支持基于日记内容的深入讨论和话题探索
  - 无缝集成到重构后的聊天系统中
  - 自动关闭模态框并跳转到聊天界面

### 🔧 技术改进
- 优化DiarySection组件，添加onChatWithDiary回调支持
- 更新ChatPage.jsx，集成日记聊天功能
- 添加MessageCircle图标支持
- 完善React性能优化，解决重渲染问题

## [1.1.0] - 2025-01-08

### ✨ 新增功能
- **AI日记功能**: 在Alex个人信息界面中集成了每日日记功能
  - AI生成符合植物学家人设的日记内容
  - 双语显示（英文原文 + 中文翻译）
  - 语音播放功能，支持TTS朗读
  - 历史日记管理和查看功能
  - 自动保存最近30天的日记记录

### 🔧 技术改进
- 新增 `DiarySection.jsx` 组件用于显示日记内容
- 新增 `DiaryHistoryModal.jsx` 组件用于管理历史日记
- 新增 `diaryService.js` 服务处理日记相关功能
- 优化CSS样式，添加日记组件专用样式
- 完善错误处理和离线模式支持

### 📚 文档更新
- 新增 `DIARY_FEATURE_GUIDE.md` 详细功能说明
- 创建 `diary-demo.html` 功能演示页面
- 更新 `README.md` 添加日记功能介绍
- 新增测试页面 `test-diary.html` 用于功能验证

### 🎨 界面优化
- 日记功能集成到Alex个人信息模态框中
- 采用选项卡设计，个人信息和日记分离
- 模态框支持响应式大小调整
- 支持深色/浅色主题切换
- 添加加载动画和交互反馈

### 🐛 修复问题
- 优化AI服务调用的错误处理
- 改进本地存储的数据管理
- 修复可能的内存泄漏问题

---

## [1.0.0] - 2025-01-07

### 🎉 初始版本
- **英语写作助手**: 基于AI的实时语法检查和风格优化
- **英语聊天功能**: 与AI角色Alex进行英语对话练习
- **双语支持**: 中英文对照显示和翻译功能
- **语音播放**: TTS语音播放支持
- **历史管理**: 聊天记录和写作历史保存
- **主题切换**: 深色/浅色主题支持
- **响应式设计**: 适配各种设备屏幕