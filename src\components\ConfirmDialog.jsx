import React from 'react';
import { AlertTriangle, X } from 'lucide-react';

const ConfirmDialog = ({
  isOpen,
  onClose,
  onConfirm,
  title = '确认操作',
  message,
  confirmText = '确定',
  cancelText = '取消',
  isDarkMode = false,
  type = 'warning' // 'warning', 'danger', 'info'
}) => {
  if (!isOpen) return null;

  const getIconColor = () => {
    switch (type) {
      case 'danger':
        return isDarkMode ? '#D2691E' : '#B91C1C';
      case 'warning':
        return isDarkMode ? '#DAA520' : '#92400E';
      default:
        return isDarkMode ? '#4682B4' : '#1E40AF';
    }
  };

  const getConfirmButtonStyle = () => {
    switch (type) {
      case 'danger':
        return {
          backgroundColor: isDarkMode ? '#D2691E' : '#B91C1C',
          color: '#FEFCF5'
        };
      case 'warning':
        return {
          backgroundColor: isDarkMode ? '#DAA520' : '#92400E',
          color: '#FEFCF5'
        };
      default:
        return {
          backgroundColor: isDarkMode ? '#4682B4' : '#1E40AF',
          color: '#FEFCF5'
        };
    }
  };

  return (
    <>
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 z-50 transition-opacity duration-300"
        style={{
          backgroundColor: isDarkMode ? 'rgba(26, 22, 17, 0.6)' : 'rgba(93, 64, 55, 0.4)'
        }}
        onClick={onClose}
      />
      
      {/* 对话框 */}
      <div 
        className="fixed z-50 transition-all duration-300 ease-out"
        style={{
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 'min(400px, 90vw)',
          backgroundColor: isDarkMode ? '#2A241D' : '#FEFCF5',
          borderRadius: '20px',
          border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`,
          boxShadow: isDarkMode 
            ? '0 20px 60px rgba(0, 0, 0, 0.4), 0 8px 24px rgba(0, 0, 0, 0.3)' 
            : '0 20px 60px rgba(93, 64, 55, 0.15), 0 8px 24px rgba(93, 64, 55, 0.1)',
          backdropFilter: 'blur(10px)',
          overflow: 'hidden'
        }}
      >
        {/* 头部 */}
        <div 
          className="flex items-center justify-between p-6 border-b"
          style={{
            borderColor: isDarkMode ? 'rgba(74, 63, 53, 0.3)' : 'rgba(230, 215, 184, 0.5)'
          }}
        >
          <div className="flex items-center gap-3">
            <div
              className="flex items-center justify-center w-10 h-10 rounded-full"
              style={{
                backgroundColor: isDarkMode ? 'rgba(74, 63, 53, 0.3)' : 'rgba(240, 230, 210, 0.5)',
                color: getIconColor()
              }}
            >
              <AlertTriangle className="w-5 h-5" />
            </div>
            <h3 
              className="text-lg font-semibold"
              style={{
                color: isDarkMode ? '#E8DCC6' : '#5D4037',
                fontFamily: 'Georgia, "Noto Serif SC", serif'
              }}
            >
              {title}
            </h3>
          </div>
          
          <button
            onClick={onClose}
            className="p-2 rounded-full transition-all duration-200 hover:scale-110"
            style={{
              color: isDarkMode ? '#C4B59A' : '#8B4513',
              backgroundColor: isDarkMode ? 'rgba(74, 63, 53, 0.3)' : 'rgba(240, 230, 210, 0.5)',
              backdropFilter: 'blur(10px)',
              border: `1px solid ${isDarkMode ? 'rgba(74, 63, 53, 0.5)' : 'rgba(230, 215, 184, 0.7)'}`
            }}
          >
            <X className="w-4 h-4" />
          </button>
        </div>

        {/* 内容 */}
        <div className="p-6">
          <p 
            className="text-base leading-relaxed mb-6"
            style={{
              color: isDarkMode ? '#E8DCC6' : '#5D4037',
              fontFamily: 'Georgia, "Noto Serif SC", serif',
              letterSpacing: '0.05em'
            }}
          >
            {message}
          </p>

          {/* 按钮组 */}
          <div className="flex items-center justify-end gap-3">
            <button
              onClick={onClose}
              className="px-6 py-3 rounded-xl transition-all duration-200 hover:scale-105"
              style={{
                backgroundColor: isDarkMode ? 'rgba(74, 63, 53, 0.4)' : 'rgba(240, 230, 210, 0.6)',
                color: isDarkMode ? '#E8DCC6' : '#5D4037',
                border: `1px solid ${isDarkMode ? 'rgba(74, 63, 53, 0.6)' : 'rgba(230, 215, 184, 0.8)'}`,
                backdropFilter: 'blur(5px)',
                fontFamily: 'Georgia, "Noto Serif SC", serif',
                letterSpacing: '0.05em'
              }}
            >
              {cancelText}
            </button>
            
            <button
              onClick={() => {
                onConfirm();
                onClose();
              }}
              className="px-6 py-3 rounded-xl transition-all duration-200 hover:scale-105"
              style={{
                ...getConfirmButtonStyle(),
                fontFamily: 'Georgia, "Noto Serif SC", serif',
                letterSpacing: '0.05em',
                fontWeight: '500'
              }}
            >
              {confirmText}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default ConfirmDialog;