// 主题颜色配置
export const themeColors = {
  light: {
    // 背景色
    bg: {
      primary: '#F5EFE6',      // 主背景
      surface: '#FEFCF5',      // 卡片表面
      card: '#FFFEF7',         // 卡片内容
      header: '#F0E6D2',       // 头部背景
      button: '#F0E6D2',       // 按钮背景
      input: '#FFFEF7',        // 输入框背景
      inputFocus: '#FFFDF0',   // 输入框聚焦背景
    },
    // 文字颜色
    text: {
      primary: '#5D4037',      // 主要文字
      secondary: '#8B4513',    // 次要文字
      muted: '#8B7D6B',        // 静音文字
      light: '#FEFCF5',        // 浅色文字（用于深色背景）
    },
    // 边框颜色
    border: {
      primary: '#E5E7EB',      // 主要边框
      secondary: '#F0E6D2',    // 次要边框
    },
    // 强调色
    accent: {
      red: '#B91C1C',          // 红色强调
      redHover: '#991B1B',     // 红色悬停
      green: '#166534',        // 绿色强调
      greenHover: '#2D5A3D',   // 绿色悬停
      blue: '#1E40AF',         // 蓝色强调
      yellow: '#92400E',       // 黄色强调
    },
    // 建议高亮色
    suggestion: {
      grammar: {
        border: '#991B1B',
        bg: '#FEF2F2',
      },
      style: {
        border: '#1E40AF',
        bg: '#DBEAFE',
      },
      clarity: {
        border: '#92400E',
        bg: '#FEF3C7',
      }
    }
  },
  dark: {
    // 背景色
    bg: {
      primary: '#1A1611',      // 主背景
      surface: '#2A241D',      // 卡片表面
      card: '#332B22',         // 卡片内容
      header: '#2A241D',       // 头部背景
      button: '#4A3F35',       // 按钮背景
      buttonHover: '#5A4F45',  // 按钮悬停背景
      input: '#2A241D',        // 输入框背景
      inputFocus: '#332B22',   // 输入框聚焦背景
    },
    // 文字颜色
    text: {
      primary: '#E8DCC6',      // 主要文字
      secondary: '#C4B59A',    // 次要文字
      muted: '#8B7D6B',        // 静音文字
      light: '#FEFCF5',        // 浅色文字
    },
    // 边框颜色
    border: {
      primary: '#4A3F35',      // 主要边框
      secondary: '#4A3F35',    // 次要边框
    },
    // 强调色
    accent: {
      red: '#D2691E',          // 红色强调
      redHover: '#B8591A',     // 红色悬停
      green: '#D2691E',        // 绿色强调（现在是橙色）
      greenHover: '#B8591A',   // 绿色悬停（现在是橙色）
      blue: '#4682B4',         // 蓝色强调
      yellow: '#DAA520',       // 黄色强调
    },
    // 建议高亮色
    suggestion: {
      grammar: {
        border: '#D2691E',
        bg: 'rgba(210, 105, 30, 0.2)',
      },
      style: {
        border: '#4682B4',
        bg: 'rgba(70, 130, 180, 0.2)',
      },
      clarity: {
        border: '#DAA520',
        bg: 'rgba(218, 165, 32, 0.2)',
      }
    }
  }
};

// 获取当前主题的颜色
export const getThemeColors = (isDarkMode) => {
  return isDarkMode ? themeColors.dark : themeColors.light;
};

// 生成样式对象的工具函数
export const createThemeStyles = (isDarkMode) => {
  const colors = getThemeColors(isDarkMode);
  
  return {
    // 基础样式
    page: {
      backgroundColor: colors.bg.primary,
      color: colors.text.primary,
      transition: 'background-color 0.3s ease, color 0.3s ease',
    },
    
    // 卡片样式
    card: {
      backgroundColor: colors.bg.surface,
      color: colors.text.primary,
      border: `1px solid ${colors.border.primary}`,
      transition: 'background-color 0.3s ease, border-color 0.3s ease',
    },
    
    // 按钮样式
    button: {
      primary: {
        backgroundColor: colors.accent.red,
        color: colors.text.light,
        transition: 'all 0.2s ease',
        ':hover': {
          backgroundColor: colors.accent.redHover,
        }
      },
      secondary: {
        backgroundColor: colors.bg.button,
        color: colors.text.primary,
        transition: 'all 0.2s ease',
        ':hover': {
          backgroundColor: colors.bg.buttonHover || colors.border.secondary,
        }
      },
      success: {
        backgroundColor: colors.accent.green,
        color: colors.text.light,
        transition: 'all 0.2s ease',
        ':hover': {
          backgroundColor: colors.accent.greenHover,
        }
      }
    },
    
    // 输入框样式
    input: {
      backgroundColor: colors.bg.input,
      color: colors.text.primary,
      border: `1px solid ${colors.border.primary}`,
      transition: 'background-color 0.3s ease, border-color 0.3s ease',
      ':focus': {
        backgroundColor: colors.bg.inputFocus,
      }
    },
    
    // 模态框样式
    modal: {
      backdrop: {
        backgroundColor: isDarkMode ? 'rgba(26, 22, 17, 0.6)' : 'rgba(93, 64, 55, 0.4)',
        transition: 'background-color 0.3s ease',
      },
      content: {
        backgroundColor: colors.bg.card,
        color: colors.text.primary,
        border: `1px solid ${colors.border.primary}`,
        transition: 'background-color 0.3s ease, border-color 0.3s ease',
      }
    },
    
    // 建议高亮样式
    suggestion: {
      grammar: {
        borderBottom: `2px solid ${colors.suggestion.grammar.border}`,
        backgroundColor: colors.suggestion.grammar.bg,
      },
      style: {
        borderBottom: `2px solid ${colors.suggestion.style.border}`,
        backgroundColor: colors.suggestion.style.bg,
      },
      clarity: {
        borderBottom: `2px solid ${colors.suggestion.clarity.border}`,
        backgroundColor: colors.suggestion.clarity.bg,
      }
    }
  };
};

// React Hook 样式工具
export const useThemeStyles = (isDarkMode) => {
  return createThemeStyles(isDarkMode);
};

// 创建内联样式的便捷函数
export const themeStyle = (isDarkMode, styleKey, customStyles = {}) => {
  const styles = createThemeStyles(isDarkMode);
  const baseStyle = getNestedValue(styles, styleKey) || {};
  
  return {
    ...baseStyle,
    ...customStyles
  };
};

// 获取嵌套对象值的工具函数
const getNestedValue = (obj, path) => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

// 创建悬停效果的工具函数
export const createHoverHandlers = (isDarkMode, baseStyle, hoverStyle) => {
  return {
    onMouseEnter: (e) => {
      Object.assign(e.target.style, hoverStyle);
    },
    onMouseLeave: (e) => {
      Object.assign(e.target.style, baseStyle);
    }
  };
};

// 预定义的常用样式组合
export const commonStyles = {
  // 圆角按钮
  roundButton: (isDarkMode, size = 48) => {
    const colors = getThemeColors(isDarkMode);
    return {
      width: `${size}px`,
      height: `${size}px`,
      borderRadius: '12px',
      backgroundColor: colors.bg.button,
      color: colors.text.secondary,
      border: 'none',
      outline: 'none',
      padding: '0',
      margin: '0',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      transition: 'all 0.2s ease',
      cursor: 'pointer',
      appearance: 'none',
      WebkitAppearance: 'none',
      MozAppearance: 'none'
    };
  },
  
  // 文本输入框
  textInput: (isDarkMode) => {
    const colors = getThemeColors(isDarkMode);
    return {
      backgroundColor: colors.bg.input,
      color: colors.text.primary,
      border: `1px solid ${colors.border.primary}`,
      borderRadius: '12px',
      padding: '16px 20px',
      fontSize: '16px',
      fontFamily: 'Georgia, "Noto Serif SC", serif',
      transition: 'all 0.2s ease',
      outline: 'none',
    };
  },
  
  // 卡片容器
  card: (isDarkMode) => {
    const colors = getThemeColors(isDarkMode);
    return {
      backgroundColor: colors.bg.surface,
      color: colors.text.primary,
      border: `1px solid ${colors.border.primary}`,
      borderRadius: '16px',
      transition: 'background-color 0.3s ease, border-color 0.3s ease',
    };
  }
};