import React from 'react';
import { X, CheckCircle, ArrowRight, BookOpen, Lightbulb } from 'lucide-react';

const ExplanationCard = ({ suggestion, onClose, onApply }) => {
  const getCategoryInfo = (category) => {
    switch (category) {
      case 'grammar':
        return {
          title: '语法改进',
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          icon: <CheckCircle className="w-5 h-5" />
        };
      case 'style':
        return {
          title: '风格优化',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          icon: <Lightbulb className="w-5 h-5" />
        };
      case 'clarity':
        return {
          title: '清晰度提升',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          icon: <BookOpen className="w-5 h-5" />
        };
      default:
        return {
          title: '写作建议',
          color: 'text-purple-600',
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200',
          icon: <Lightbulb className="w-5 h-5" />
        };
    }
  };

  const categoryInfo = getCategoryInfo(suggestion.category);

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
      {/* 头部 */}
      <div className={`${categoryInfo.bgColor} ${categoryInfo.borderColor} border-b px-6 py-4`}>
        <div className="flex items-center justify-between">
          <div className={`flex items-center gap-2 ${categoryInfo.color}`}>
            {categoryInfo.icon}
            <h3 className="font-semibold">{categoryInfo.title}</h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* 内容 */}
      <div className="p-6 space-y-4">
        {/* 对比展示 */}
        <div className="space-y-3">
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <div className="text-sm text-red-700 font-medium mb-1">原文：</div>
            <div className="font-mono text-sm text-red-800 bg-white px-2 py-1 rounded border">
              "{suggestion.original}"
            </div>
          </div>

          <div className="flex justify-center">
            <ArrowRight className="w-5 h-5 text-gray-400" />
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="text-sm text-green-700 font-medium mb-1">建议修改：</div>
            <div className="font-mono text-sm text-green-800 bg-white px-2 py-1 rounded border">
              "{suggestion.suggested}"
            </div>
          </div>
        </div>

        {/* 详细解释 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
            <BookOpen className="w-4 h-4 text-primary-600" />
            为什么这样更好？
          </h4>
          <p className="text-sm text-gray-700 leading-relaxed">
            {suggestion.explanation || suggestion.shortExplanation || '这个修改能让你的表达更加清晰和地道。'}
          </p>
        </div>

        {/* 语法规则或写作技巧 */}
        {suggestion.rule && (
          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">📚 相关知识点</h4>
            <p className="text-sm text-blue-800">
              {suggestion.rule}
            </p>
          </div>
        )}

        {/* 示例 */}
        {suggestion.examples && suggestion.examples.length > 0 && (
          <div className="bg-yellow-50 rounded-lg p-4">
            <h4 className="font-medium text-yellow-900 mb-2">💡 类似例子</h4>
            <div className="space-y-2">
              {suggestion.examples.map((example, index) => (
                <div key={index} className="text-sm">
                  <div className="text-yellow-800">
                    <span className="font-mono bg-white px-1 rounded">"{example.before}"</span>
                    <ArrowRight className="w-3 h-3 inline mx-2" />
                    <span className="font-mono bg-white px-1 rounded">"{example.after}"</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex gap-3 pt-4 border-t border-gray-200">
          <button
            onClick={onApply}
            className="flex-1 bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors font-medium"
          >
            应用此建议
          </button>
          <button
            onClick={onClose}
            className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors font-medium"
          >
            稍后处理
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExplanationCard;
