import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Cloud, Sun, CloudRain, CloudSnow, CloudLightning } from 'lucide-react';

// 中国传统节气数据
const solarTerms = [
    { name: '立春', month: 2, day: 4 },
    { name: '雨水', month: 2, day: 19 },
    { name: '惊蛰', month: 3, day: 6 },
    { name: '春分', month: 3, day: 21 },
    { name: '清明', month: 4, day: 5 },
    { name: '谷雨', month: 4, day: 20 },
    { name: '立夏', month: 5, day: 6 },
    { name: '小满', month: 5, day: 21 },
    { name: '芒种', month: 6, day: 6 },
    { name: '夏至', month: 6, day: 21 },
    { name: '小暑', month: 7, day: 7 },
    { name: '大暑', month: 7, day: 23 },
    { name: '立秋', month: 8, day: 8 },
    { name: '处暑', month: 8, day: 23 },
    { name: '白露', month: 9, day: 8 },
    { name: '秋分', month: 9, day: 23 },
    { name: '寒露', month: 10, day: 8 },
    { name: '霜降', month: 10, day: 24 },
    { name: '立冬', month: 11, day: 8 },
    { name: '小雪', month: 11, day: 22 },
    { name: '大雪', month: 12, day: 7 },
    { name: '冬至', month: 12, day: 22 },
    { name: '小寒', month: 1, day: 6 },
    { name: '大寒', month: 1, day: 20 }
];

// 根据日期获取当前节气
const getCurrentSolarTerm = (date) => {
    // 找到最接近当前日期的节气
    let closestTerm = null;
    let minDiff = Infinity;

    solarTerms.forEach(term => {
        // 计算日期差值（简化版，实际节气计算更复杂）
        const termDate = new Date(date.getFullYear(), term.month - 1, term.day);
        const diff = Math.abs(date.getTime() - termDate.getTime());

        if (diff < minDiff) {
            minDiff = diff;
            closestTerm = term;
        }
    });

    return closestTerm ? closestTerm.name : '未知节气';
};

// 模拟天气数据（实际应用中应该从API获取）
const getWeatherIcon = () => {
    const weatherTypes = [
        { type: 'sunny', icon: <Sun className="w-5 h-5" style={{ color: '#F59E0B' }} /> },
        { type: 'cloudy', icon: <Cloud className="w-5 h-5" style={{ color: '#60A5FA' }} /> },
        { type: 'rainy', icon: <CloudRain className="w-5 h-5" style={{ color: '#3B82F6' }} /> },
        { type: 'snowy', icon: <CloudSnow className="w-5 h-5" style={{ color: '#93C5FD' }} /> },
        { type: 'stormy', icon: <CloudLightning className="w-5 h-5" style={{ color: '#6366F1' }} /> }
    ];

    // 这里只是随机选择一个天气图标，实际应用中应该基于真实天气数据
    const randomIndex = Math.floor(Math.random() * weatherTypes.length);
    return weatherTypes[randomIndex].icon;
};

const DateTimeWeather = ({ isDarkMode = false }) => {
    const [currentDate, setCurrentDate] = useState(new Date());
    const [weatherIcon, setWeatherIcon] = useState(null);

    useEffect(() => {
        // 每秒更新一次时间
        const timer = setInterval(() => {
            setCurrentDate(new Date());
        }, 1000);

        // 设置初始天气图标
        setWeatherIcon(getWeatherIcon());

        // 每小时更新一次天气图标（模拟）
        const weatherTimer = setInterval(() => {
            setWeatherIcon(getWeatherIcon());
        }, 3600000); // 1小时

        return () => {
            clearInterval(timer);
            clearInterval(weatherTimer);
        };
    }, []);

    // 格式化日期
    const formattedDate = currentDate.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
    });

    // 格式化时间
    const formattedTime = currentDate.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    // 获取当前节气
    const currentSolarTerm = getCurrentSolarTerm(currentDate);

    return (
        <div className="flex items-center gap-3" style={{ color: isDarkMode ? '#E8DCC6' : '#5D4037' }}>
            <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                color: isDarkMode ? '#D2691E' : '#166534',
                fontFamily: 'Georgia, "Noto Serif SC", serif',
                letterSpacing: '0.05em'
            }}>
                <Calendar className="w-5 h-5" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                <span style={{ fontSize: '16px' }}>{formattedDate}</span>
            </div>

            <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                color: isDarkMode ? '#D2691E' : '#166534',
                fontFamily: 'Georgia, "Noto Serif SC", serif',
                letterSpacing: '0.05em',
                marginLeft: '16px'
            }}>
                <Clock className="w-5 h-5" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                <span style={{ fontSize: '16px' }}>{formattedTime}</span>
            </div>

            <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                color: isDarkMode ? '#D2691E' : '#166534',
                fontFamily: 'Georgia, "Noto Serif SC", serif',
                letterSpacing: '0.05em',
                marginLeft: '16px',
                backgroundColor: isDarkMode ? '#4A3F35' : '#F0E6D2',
                padding: '4px 12px',
                borderRadius: '16px'
            }}>
                {weatherIcon}
                <span style={{ fontSize: '16px' }}>{currentSolarTerm}</span>
            </div>
        </div>
    );
};

export default DateTimeWeather;