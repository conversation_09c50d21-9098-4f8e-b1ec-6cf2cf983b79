/**
 * 聊天历史记录服务
 * 管理英语聊天的历史记录存储和检索
 */

const CHAT_HISTORY_KEY = 'english_chat_history';
const CURRENT_SESSION_KEY = 'current_chat_session_id';
const MAX_HISTORY_RECORDS = 50; // 最多保存50条聊天记录

/**
 * 开始新的聊天会话
 * @returns {string} 新会话的ID
 */
export const startNewChatSession = () => {
  const sessionId = Date.now().toString();
  localStorage.setItem(CURRENT_SESSION_KEY, sessionId);
  console.log('🆕 开始新的聊天会话:', sessionId);
  return sessionId;
};

/**
 * 获取当前会话ID
 * @returns {string|null} 当前会话ID
 */
export const getCurrentSessionId = () => {
  return localStorage.getItem(CURRENT_SESSION_KEY);
};

/**
 * 保存或更新聊天会话
 * @param {Array} messages - 消息数组
 * @param {string} sessionId - 会话ID（可选，如果不提供则使用当前会话）
 * @param {string} sessionTitle - 会话标题（可选）
 */
export const saveChatSession = (messages, sessionId = null, sessionTitle = null) => {
  try {
    // 如果没有提供sessionId，使用当前会话ID
    const currentSessionId = sessionId || getCurrentSessionId();
    
    // 如果没有当前会话ID，创建新会话
    if (!currentSessionId) {
      const newSessionId = startNewChatSession();
      return saveChatSession(messages, newSessionId, sessionTitle);
    }

    // 过滤有效消息
    const validMessages = messages.filter(msg => msg.type === 'user' || msg.type === 'ai');
    if (validMessages.length === 0) {
      console.log('没有有效消息，跳过保存');
      return null;
    }

    const history = getChatHistory();
    
    // 查找现有会话
    const existingSessionIndex = history.findIndex(session => session.id === currentSessionId);
    
    // 生成会话标题
    const title = sessionTitle || generateSessionTitle(validMessages);
    
    const sessionData = {
      id: currentSessionId,
      title,
      messages: validMessages,
      timestamp: new Date().toISOString(),
      messageCount: validMessages.filter(msg => msg.type === 'user').length,
      lastUpdated: new Date().toISOString()
    };

    if (existingSessionIndex >= 0) {
      // 更新现有会话
      history[existingSessionIndex] = sessionData;
      console.log('📝 更新现有会话:', currentSessionId, '消息数:', validMessages.length);
    } else {
      // 创建新会话记录
      history.unshift(sessionData);
      console.log('🆕 创建新会话记录:', currentSessionId, '消息数:', validMessages.length);
    }

    // 限制历史记录数量
    if (history.length > MAX_HISTORY_RECORDS) {
      history.splice(MAX_HISTORY_RECORDS);
    }

    localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(history));
    return sessionData;
  } catch (error) {
    console.error('保存聊天历史失败:', error);
    return null;
  }
};

/**
 * 检查两个会话是否重复
 * @param {Object} session1 - 会话1
 * @param {Object} session2 - 会话2
 * @returns {boolean} 是否重复
 */
const isDuplicateSession = (session1, session2) => {
  // 如果消息数量不同，不是重复
  if (session1.messageCount !== session2.messageCount) {
    return false;
  }
  
  // 如果消息数量相同且都只有1条或更少，比较标题
  if (session1.messageCount <= 1) {
    return session1.title === session2.title;
  }
  
  // 比较最后几条用户消息的内容
  const userMessages1 = session1.messages.filter(msg => msg.type === 'user');
  const userMessages2 = session2.messages.filter(msg => msg.type === 'user');
  
  if (userMessages1.length !== userMessages2.length) {
    return false;
  }
  
  // 比较最后两条用户消息（如果有的话）
  const compareCount = Math.min(2, userMessages1.length);
  for (let i = 0; i < compareCount; i++) {
    const msg1 = userMessages1[userMessages1.length - 1 - i];
    const msg2 = userMessages2[userMessages2.length - 1 - i];
    if (msg1.content !== msg2.content) {
      return false;
    }
  }
  
  return true;
};

/**
 * 获取所有聊天历史记录
 * @returns {Array} 历史记录数组
 */
export const getChatHistory = () => {
  try {
    const history = localStorage.getItem(CHAT_HISTORY_KEY);
    return history ? JSON.parse(history) : [];
  } catch (error) {
    console.error('获取聊天历史失败:', error);
    return [];
  }
};

/**
 * 删除指定的聊天记录
 * @param {number} sessionId - 会话ID
 */
export const deleteChatSession = (sessionId) => {
  try {
    const history = getChatHistory();
    const filteredHistory = history.filter(session => session.id !== sessionId);
    localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(filteredHistory));
    return true;
  } catch (error) {
    console.error('删除聊天记录失败:', error);
    return false;
  }
};

/**
 * 清空所有聊天历史记录
 */
export const clearChatHistory = () => {
  try {
    localStorage.removeItem(CHAT_HISTORY_KEY);
    return true;
  } catch (error) {
    console.error('清空聊天历史失败:', error);
    return false;
  }
};

/**
 * 根据消息内容生成会话标题
 * @param {Array} messages - 消息数组
 * @returns {string} 生成的标题
 */
const generateSessionTitle = (messages) => {
  // 找到所有用户消息
  const userMessages = messages.filter(msg => msg.type === 'user');
  
  if (userMessages.length > 0) {
    // 如果有多条用户消息，尝试找到最有代表性的一条
    let titleMessage = userMessages[0];
    
    // 如果第一条消息太短（比如只是打招呼），尝试使用第二条
    if (userMessages.length > 1 && titleMessage.content.length < 10) {
      titleMessage = userMessages[1];
    }
    
    // 清理标题，移除常见的开场白
    let title = titleMessage.content
      .replace(/^(hi|hello|hey|good morning|good afternoon|good evening)[,!.\s]*/i, '')
      .trim();
    
    // 如果清理后为空，使用原始内容
    if (!title) {
      title = titleMessage.content;
    }
    
    // 限制长度并添加省略号
    if (title.length > 40) {
      title = title.substring(0, 40) + '...';
    }
    
    return title || 'New Chat';
  }
  
  // 如果没有用户消息，使用时间戳
  const now = new Date();
  return `Chat ${now.getMonth() + 1}/${now.getDate()} ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;
};

/**
 * 清理重复的聊天记录
 * @returns {number} 清理的记录数量
 */
export const cleanupDuplicateHistory = () => {
  try {
    const history = getChatHistory();
    const originalLength = history.length;
    
    // 新的去重逻辑：基于会话的第一条用户消息和消息总数
    const uniqueHistory = [];
    const seen = new Map();
    
    for (const session of history) {
      const userMessages = session.messages.filter(msg => msg.type === 'user');
      
      if (userMessages.length === 0) {
        // 如果没有用户消息，跳过这个会话
        continue;
      }
      
      // 使用第一条用户消息作为会话的唯一标识
      const firstUserMessage = userMessages[0].content.trim().toLowerCase();
      const messageCount = userMessages.length;
      const uniqueKey = `${firstUserMessage}`;
      
      // 如果这是同一个对话的不同阶段，保留消息数最多的版本
      if (seen.has(uniqueKey)) {
        const existingSession = seen.get(uniqueKey);
        if (messageCount > existingSession.messageCount) {
          // 替换为消息更多的版本
          const existingIndex = uniqueHistory.findIndex(s => s.id === existingSession.id);
          if (existingIndex >= 0) {
            uniqueHistory[existingIndex] = session;
            seen.set(uniqueKey, session);
          }
        }
        // 否则跳过这个重复的会话
      } else {
        seen.set(uniqueKey, session);
        uniqueHistory.push(session);
      }
    }
    
    // 按时间戳排序（最新的在前）
    uniqueHistory.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    // 如果有重复记录被清理，更新localStorage
    if (uniqueHistory.length < originalLength) {
      localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(uniqueHistory));
      console.log(`🧹 清理了 ${originalLength - uniqueHistory.length} 条重复记录，保留 ${uniqueHistory.length} 条`);
      return originalLength - uniqueHistory.length;
    }
    
    return 0;
  } catch (error) {
    console.error('清理重复记录失败:', error);
    return 0;
  }
};

/**
 * 获取聊天统计信息
 * @returns {Object} 统计信息
 */
export const getChatStats = () => {
  try {
    const history = getChatHistory();
    const totalSessions = history.length;
    const totalMessages = history.reduce((sum, session) => sum + session.messageCount, 0);
    
    return {
      totalSessions,
      totalMessages,
      lastChatDate: history.length > 0 ? history[0].timestamp : null
    };
  } catch (error) {
    console.error('获取聊天统计失败:', error);
    return {
      totalSessions: 0,
      totalMessages: 0,
      lastChatDate: null
    };
  }
};