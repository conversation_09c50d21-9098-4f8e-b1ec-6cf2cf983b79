import React from 'react';
import { useTheme } from '../ThemeProvider';

const ThemedCard = ({ 
  children, 
  className = '', 
  style = {},
  padding = 'md',
  ...props 
}) => {
  const { isDarkMode } = useTheme();

  // 基础样式
  const baseStyles = {
    backgroundColor: 'var(--color-bg-secondary)',
    color: 'var(--color-text-primary)',
    border: '1px solid var(--color-border)',
    borderRadius: '16px',
    transition: 'background-color 0.3s ease, border-color 0.3s ease',
  };

  // 内边距样式
  const paddingStyles = {
    none: { padding: '0' },
    sm: { padding: '16px' },
    md: { padding: '24px' },
    lg: { padding: '32px' },
    xl: { padding: '48px' },
  };

  // 合并样式
  const finalStyles = {
    ...baseStyles,
    ...paddingStyles[padding],
    ...style,
  };

  return (
    <div
      className={`themed-card ${className}`}
      style={finalStyles}
      {...props}
    >
      {children}
    </div>
  );
};

export default ThemedCard;