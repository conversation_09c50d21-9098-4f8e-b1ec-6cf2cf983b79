import React, { useState, useEffect } from 'react';
import { Edit3 } from 'lucide-react';

const ChatEdgeNavigationArrow = ({ onBackToEditor, isDarkMode }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 检测是否为移动设备
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || 'ontouchstart' in window);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    let timeoutId = null;
    let showTimeoutId = null;

    const handleMouseMove = (e) => {
      // 在移动设备上不启用鼠标移动触发
      if (isMobile) return;
      
      const screenWidth = window.innerWidth;
      const mouseX = e.clientX;
      const edgeThreshold = 80; // 距离右边缘80px内触发

      // 当鼠标接近右边缘时显示箭头
      if (screenWidth - mouseX <= edgeThreshold) {
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
        // 添加轻微延迟，避免意外触发
        if (!showTimeoutId && !isVisible) {
          showTimeoutId = setTimeout(() => {
            setIsVisible(true);
            showTimeoutId = null;
          }, 100);
        }
      } else {
        // 清除显示延迟
        if (showTimeoutId) {
          clearTimeout(showTimeoutId);
          showTimeoutId = null;
        }
        // 延迟隐藏，避免频繁闪烁
        if (!timeoutId && isVisible && !isHovered) {
          timeoutId = setTimeout(() => {
            setIsVisible(false);
            timeoutId = null;
          }, 500);
        }
      }
    };

    // 添加鼠标移动监听器（仅在非移动设备上）
    if (!isMobile) {
      document.addEventListener('mousemove', handleMouseMove);
    }

    // 清理函数
    return () => {
      if (!isMobile) {
        document.removeEventListener('mousemove', handleMouseMove);
      }
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      if (showTimeoutId) {
        clearTimeout(showTimeoutId);
      }
    };
  }, [isHovered, isVisible, isMobile]);

  const handleMouseEnter = () => {
    setIsHovered(true);
    setIsVisible(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    // 延迟隐藏，给用户时间重新悬停
    setTimeout(() => {
      setIsVisible(false);
    }, 800);
  };

  return (
    <div
      className="fixed right-0 top-1/2 z-50"
      style={{
        transition: 'all 0.3s ease-in-out',
        opacity: (isVisible || isMobile) ? (isMobile ? 0.8 : 1) : 0,
        transform: (isVisible || isMobile) 
          ? 'translateY(-50%) translateX(0)' 
          : 'translateY(-50%) translateX(100%)',
        pointerEvents: (isVisible || isMobile) ? 'auto' : 'none'
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 触发区域 - 透明的扩展区域（仅在桌面端） */}
      {!isMobile && (
        <div
          className="absolute right-0 top-0 h-full"
          style={{
            width: '60px', // 触发区域宽度
            transform: 'translateX(100%)',
            pointerEvents: 'auto'
          }}
        />
      )}
      
      {/* 箭头按钮 */}
      <button
        onClick={onBackToEditor}
        className="group"
        style={{
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transition: 'all 0.3s ease-in-out',
          backgroundColor: isDarkMode ? '#D2691E' : '#166534',
          color: '#FEFCF5',
          borderRadius: isMobile ? '8px 0 0 8px' : '12px 0 0 12px',
          padding: isMobile ? '12px 8px' : '16px 12px',
          border: 'none',
          cursor: 'pointer',
          boxShadow: isDarkMode 
            ? '0 4px 12px rgba(210, 105, 30, 0.3)' 
            : '0 4px 12px rgba(22, 101, 52, 0.3)',
          transform: isMobile 
            ? 'translateX(0) scale(0.9)' 
            : isHovered 
            ? 'translateX(-8px) scale(1.05)' 
            : 'translateX(0) scale(1)',
        }}
        title="返回写作模式"
      >
        {/* 图标 */}
        <Edit3 
          className="w-6 h-6 transition-transform duration-300"
          style={{
            transform: isHovered ? 'scale(1.1)' : 'scale(1)'
          }}
        />

        {/* 脉冲动画效果 */}
        <div
          className={`absolute inset-0 rounded-l-xl transition-all duration-1000 ${
            isVisible && !isHovered ? 'animate-pulse' : ''
          }`}
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            pointerEvents: 'none'
          }}
        />
      </button>
    </div>
  );
};

export default ChatEdgeNavigationArrow;