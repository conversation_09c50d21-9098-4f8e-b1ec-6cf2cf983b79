# WordTutor - AI英语写作助手

一个基于React的智能英语写作助手，集成了Qwen3-32B大语言模型，提供实时的语法检查、风格优化和写作建议。

## 🌟 主要功能

### 📝 写作助手功能
- **实时AI分析**: 使用Qwen3-32B模型进行智能文本分析
- **语法检查**: 自动识别并修正语法错误
- **风格优化**: 提供写作风格改进建议
- **清晰度提升**: 帮助提高文本的可读性和表达清晰度
- **详细解释**: 为每个建议提供详细的解释和示例
- **一键应用**: 快速应用AI建议到文本中

### 💬 英语聊天功能
- **AI对话**: 与Alex（植物学家和自然摄影师）进行英语对话
- **实时翻译**: 提供中英文对照，帮助理解
- **语音播放**: TTS语音播放功能，练习听力和发音
- **表达建议**: 点击用户消息获取更地道的表达建议
- **聊天历史**: 保存和管理对话记录

### 📖 AI日记功能 ✨ 新功能
- **每日日记**: AI生成符合Alex人设的每日日记内容
- **双语阅读**: 英文原文配中文翻译，提升阅读理解
- **语音朗读**: 支持日记内容的语音播放
- **AI生图**: 根据日记内容智能生成National Geographic风格的自然摄影图像
- **日记聊天**: 点击日记旁的聊天按钮，与AI深入讨论日记话题
- **历史管理**: 查看和管理所有历史日记记录
- **学习材料**: 丰富的植物学和自然科学词汇

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置API密钥

有两种方式配置SiliconFlow API密钥：

#### 方式一：通过界面配置（推荐）
1. 启动应用后，点击右上角的"配置API"按钮
2. 在弹出的对话框中输入你的SiliconFlow API密钥
3. 点击"测试连接"验证密钥有效性
4. 保存配置

#### 方式二：通过环境变量
1. 复制 `.env.example` 为 `.env`
2. 在 `.env` 文件中填入你的API密钥：
   ```
   REACT_APP_SILICONFLOW_API_KEY=your_api_key_here
   ```

### 3. 获取API密钥

访问 [SiliconFlow](https://cloud.siliconflow.cn/) 注册账号并获取API密钥。

### 4. 启动应用

```bash
npm run dev
```

应用将在 http://localhost:3000 启动。

## 📖 使用说明

### 基本使用
1. 在文本编辑器中输入英文内容
2. 系统会自动进行实时分析（输入10个字符后）
3. 点击"AI分析"按钮进行手动分析
4. 查看右侧的建议面板
5. 点击建议查看详细解释
6. 点击"应用"按钮采纳建议

### AI分析功能
- **自动分析**: 停止输入1秒后自动触发
- **手动分析**: 点击"AI分析"按钮主动触发
- **实时反馈**: 显示分析进度和结果

### 建议类型
- 🔴 **语法问题**: 语法错误和不规范用法
- 🔵 **风格优化**: 提升写作风格和表达力
- 🟢 **清晰度**: 改善文本可读性和逻辑性

### 📖 AI日记功能使用

1. **访问日记功能**
   - 在英语聊天页面中点击Alex的头像（🌿）
   - 在个人信息界面中选择"📖 日记"选项卡

2. **查看今日日记**
   - 在日记选项卡中查看Alex的每日发现和感受
   - 显示符合植物学家人设的专业内容

3. **生成新日记**
   - 点击"新日记"按钮生成今日的AI日记内容
   - 内容符合Alex的人设，包含植物学知识和自然观察

4. **双语阅读**
   - 英文原文提供地道的表达和丰富词汇
   - 点击"显示中文翻译"查看对照翻译

5. **语音学习**
   - 点击语音播放按钮听取标准英语发音
   - 支持自动播放功能（在设置中开启）

6. **历史管理**
   - 点击历史按钮查看所有保存的日记
   - 支持前后导航和删除功能
   - 自动保存最近30天的日记记录

## 🛠️ 技术栈

- **前端**: React 18 + Vite
- **样式**: Tailwind CSS
- **图标**: Lucide React
- **AI模型**: Qwen3-32B (通过SiliconFlow API)
- **状态管理**: React Hooks

## 📁 项目结构

```
src/
├── components/          # React组件
│   ├── Header.jsx      # 头部组件
│   ├── VintageTextEditor.jsx  # 文本编辑器
│   ├── EnglishChatPage.jsx    # 英语聊天页面
│   ├── ExplanationCard.jsx    # 解释卡片
│   ├── VintageApiConfigModal.jsx  # API配置弹窗
│   ├── SuggestionBubble.jsx   # 建议气泡
│   ├── VoicePlayButton.jsx    # 语音播放按钮
│   └── DateTimeWeather.jsx    # 日期时间天气组件
├── services/           # 服务层
│   ├── aiService.js    # AI分析服务
│   ├── ttsService.js   # 语音合成服务
│   └── chatHistoryService.js  # 聊天历史服务
├── config/             # 配置文件
│   └── apiConfig.js    # API配置
├── utils/              # 工具函数
│   └── themeUtils.js   # 主题工具
├── App.jsx             # 主应用组件
└── style.css           # 全局样式
```

## 🔧 配置选项

在 `src/config/apiConfig.js` 中可以调整以下配置：

- `temperature`: AI创造性参数 (0.0-1.0)
- `maxTokens`: 最大返回token数
- `timeout`: 请求超时时间
- `maxRetries`: 最大重试次数

## 🔒 隐私和安全

- API密钥安全存储在浏览器本地存储中
- 不会上传到任何第三方服务器
- 文本分析通过SiliconFlow API进行，遵循其隐私政策

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 🆘 常见问题

### Q: API密钥配置后仍然使用演示数据？
A: 请确保API密钥正确，并点击"测试连接"验证。如果问题持续，请检查浏览器控制台的错误信息。

### Q: 分析速度较慢？
A: AI分析需要调用远程API，速度取决于网络状况和API响应时间。通常需要2-5秒。

### Q: 支持哪些语言？
A: 目前主要针对英文写作优化，中文界面但分析英文内容。

### Q: 日记功能如何工作？
A: AI日记功能使用豆包API生成符合Alex人设的日记内容。如果API不可用，会使用预设的示例内容。

### Q: 日记内容会重复吗？
A: 每次生成的日记内容都是独特的，AI会根据当前日期和随机因素创造不同的植物学发现和观察。

### Q: 可以删除不喜欢的日记吗？
A: 可以。在日记历史模态框中，每篇日记都有删除按钮，点击即可删除不需要的日记。

---

如有问题，请提交Issue或联系开发者。
