import { useState } from 'react';

export const useSuggestionHandler = (text, setText, suggestions, setSuggestions) => {
  const [appliedSuggestions, setAppliedSuggestions] = useState(new Set());

  // 应用建议
  const applySuggestion = (suggestion) => {
    if (!suggestion.canApply || appliedSuggestions.has(suggestion.id)) {
      return;
    }

    let newText = text;

    if (suggestion.type === 'capitalization') {
      // 处理大小写修正 - 从后往前替换避免位置偏移
      const sortedPositions = [...suggestion.positions].sort((a, b) => b.start - a.start);
      sortedPositions.forEach(pos => {
        const before = newText.substring(0, pos.start);
        const after = newText.substring(pos.end);
        newText = before + suggestion.replacement + after;
      });
    } else if (suggestion.type === 'spelling') {
      // 处理拼写错误修正
      const pos = suggestion.positions[0];
      const before = newText.substring(0, pos.start);
      const after = newText.substring(pos.end);
      newText = before + suggestion.replacement + after;
    } else if (suggestion.type === 'punctuation' && suggestion.isAppend) {
      // 处理句尾标点添加
      newText = text.trim() + suggestion.replacement;
    } else if (suggestion.positions && suggestion.positions.length > 0) {
      // 处理一般的文本替换
      const pos = suggestion.positions[0]; // 取第一个位置
      const before = newText.substring(0, pos.start);
      const after = newText.substring(pos.end);
      newText = before + suggestion.replacement + after;
    }

    // 更新文本
    setText(newText);

    // 标记建议为已应用
    setAppliedSuggestions(prev => new Set([...prev, suggestion.id]));

    // 延迟移除已应用的建议及相关重叠建议，给用户看到反馈
    setTimeout(() => {
      // 获取应用建议后的新文本
      const updatedText = newText;
      
      // 移除当前建议以及与之重叠或已失效的建议
      setSuggestions(prev => {
        return prev.filter(s => {
          // 移除当前应用的建议
          if (s.id === suggestion.id) return false;
          
          // 检查建议是否仍然有效（原文是否仍然存在）
          if (s.positions && s.positions.length > 0) {
            const pos = s.positions[0];
            const originalText = s.original;
            const textAtPosition = updatedText.substring(pos.start, pos.end);
            
            // 如果原文已经被修改，则该建议不再有效
            if (textAtPosition !== originalText) {
              return false;
            }
            
            // 检查是否与当前应用的建议位置重叠
            if (suggestion.positions && suggestion.positions.length > 0) {
              const appliedPos = suggestion.positions[0];
              // 检查位置是否重叠
              if ((pos.start >= appliedPos.start && pos.start < appliedPos.end) ||
                  (pos.end > appliedPos.start && pos.end <= appliedPos.end) ||
                  (pos.start <= appliedPos.start && pos.end >= appliedPos.end)) {
                return false;
              }
            }
          }
          
          return true;
        });
      });
      
      // 清理已应用建议的记录
      setAppliedSuggestions(prev => {
        const newSet = new Set(prev);
        newSet.delete(suggestion.id);
        return newSet;
      });
    }, 1500);
  };

  // 应用所有可应用的建议
  const applyAllSuggestions = () => {
    const applicableSuggestions = suggestions.filter(s =>
      s.canApply && !appliedSuggestions.has(s.id)
    );

    if (applicableSuggestions.length === 0) return;

    // 按位置从后往前排序，避免位置偏移问题
    const sortedSuggestions = applicableSuggestions.sort((a, b) => {
      const aPos = a.positions?.[0]?.start || 0;
      const bPos = b.positions?.[0]?.start || 0;
      return bPos - aPos;
    });

    let newText = text;
    const appliedIds = new Set();

    sortedSuggestions.forEach(suggestion => {
      if (suggestion.type === 'capitalization') {
        const sortedPositions = [...suggestion.positions].sort((a, b) => b.start - a.start);
        sortedPositions.forEach(pos => {
          const before = newText.substring(0, pos.start);
          const after = newText.substring(pos.end);
          newText = before + suggestion.replacement + after;
        });
      } else if (suggestion.type === 'punctuation' && suggestion.isAppend) {
        newText = newText.trim() + suggestion.replacement;
      } else if (suggestion.positions && suggestion.positions.length > 0) {
        const pos = suggestion.positions[0];
        const before = newText.substring(0, pos.start);
        const after = newText.substring(pos.end);
        newText = before + suggestion.replacement + after;
      }
      appliedIds.add(suggestion.id);
    });

    setText(newText);
    setAppliedSuggestions(prev => new Set([...prev, ...appliedIds]));

    // 延迟移除已应用的建议及相关重叠建议
    setTimeout(() => {
      // 获取应用所有建议后的新文本
      const updatedText = newText;
      
      // 移除已应用的建议以及与之重叠或已失效的建议
      setSuggestions(prev => {
        return prev.filter(s => {
          // 移除已应用的建议
          if (appliedIds.has(s.id)) return false;
          
          // 检查建议是否仍然有效（原文是否仍然存在）
          if (s.positions && s.positions.length > 0) {
            const pos = s.positions[0];
            const originalText = s.original;
            
            // 确保位置在文本范围内
            if (pos.start >= updatedText.length || pos.end > updatedText.length) {
              return false;
            }
            
            const textAtPosition = updatedText.substring(pos.start, pos.end);
            
            // 如果原文已经被修改，则该建议不再有效
            if (textAtPosition !== originalText) {
              return false;
            }
            
            // 检查是否与任何已应用的建议位置重叠
            for (const appliedSuggestion of sortedSuggestions) {
              if (appliedSuggestion.positions && appliedSuggestion.positions.length > 0) {
                const appliedPos = appliedSuggestion.positions[0];
                // 检查位置是否重叠
                if ((pos.start >= appliedPos.start && pos.start < appliedPos.end) ||
                    (pos.end > appliedPos.start && pos.end <= appliedPos.end) ||
                    (pos.start <= appliedPos.start && pos.end >= appliedPos.end)) {
                  return false;
                }
              }
            }
          }
          
          return true;
        });
      });
      
      setAppliedSuggestions(new Set());
    }, 1500);
  };

  // 忽略建议
  const dismissSuggestion = (suggestion) => {
    setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));
  };

  return {
    appliedSuggestions,
    applySuggestion,
    applyAllSuggestions,
    dismissSuggestion
  };
};
