import React from 'react';
import { BookO<PERSON>, Settings, Maximize } from 'lucide-react';

const Header = ({ onShowApiConfig, onToggleImmersiveMode, isDarkMode }) => {
  return (
    <div
      className="transition-colors duration-300"
      style={{ backgroundColor: isDarkMode ? '#2A241D' : '#F0E6D2' }}
    >
      <div className="max-w-7xl mx-auto py-6">
        <div className="flex items-center justify-between" style={{ paddingLeft: '80px', paddingRight: '80px' }}>
          <div className="flex items-center gap-4">
            <div
              className="flex items-center justify-center w-12 h-12 rounded-xl transition-colors duration-300"
              style={{ backgroundColor: isDarkMode ? '#D2691E' : '#B91C1C' }}
            >
              <BookOpen className="w-7 h-7" style={{ color: '#FEFCF5' }} />
            </div>
            <div>
              <h1
                className="text-3xl font-bold transition-colors duration-300"
                style={{
                  color: isDarkMode ? '#E8DCC6' : '#5D4037',
                  fontFamily: 'Georgia, "Noto Serif SC", serif',
                  letterSpacing: '0.05em'
                }}
              >
                Studybuddy
              </h1>
              <p
                className="transition-colors duration-300"
                style={{
                  color: isDarkMode ? '#C4B59A' : '#8B4513',
                  fontSize: '16px',
                  marginTop: '4px',
                  letterSpacing: '0.05em'
                }}
              >
                你的英语写作搭子
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={onToggleImmersiveMode}
              className="header-btn"
              title="沉浸写作模式"
            >
              <Maximize className="w-6 h-6" />
            </button>
            <button
              onClick={onShowApiConfig}
              className="header-btn"
              title="设置"
            >
              <Settings className="w-6 h-6" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
