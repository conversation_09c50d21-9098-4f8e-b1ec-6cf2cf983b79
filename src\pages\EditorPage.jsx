
import React, { useState, useEffect } from 'react';
import Header from '../components/Header';
import VintageTextEditor from '../components/VintageTextEditor';
import SuggestionBubble from '../components/SuggestionBubble';
import AIResponseSidebar from '../components/AIResponseSidebar';
import HistoryModal from '../components/HistoryModal';
import TextSelectionMenu from '../components/TextSelectionMenu';
import EdgeNavigationArrow from '../components/EdgeNavigationArrow';
import ConfirmDialog from '../components/ConfirmDialog';
import useAIAnalysis from '../hooks/useAIAnalysis';
import { useSuggestionHandler } from '../hooks/useSuggestionHandler';
import { useImmersiveMode } from '../hooks/useImmersiveMode';
import { useConfirmDialog } from '../hooks/useConfirmDialog';
import { useAppContext } from '../context/AppContext';
import { getWritingText, getWritingSuggestions, autoSaveWritingText, clearWritingText } from '../services/writingTextService';

export default function EditorPage() {
  const { state, dispatch, openModal, closeModal, isModalOpen } = useAppContext();
  const { isDarkMode } = state;

  // 内部状态 - 从localStorage恢复文本
  const [text, setText] = useState(() => getWritingText());
  const [apiKey] = useState(localStorage.getItem('doubao_api_key') || '');
  const [hoveredSuggestion, setHoveredSuggestion] = useState(null);
  const [activeBubble, setActiveBubble] = useState(null);
  const [bubblePosition, setBubblePosition] = useState({ x: 0, y: 0 });

  // 自定义Hooks
  const { isAnalyzing, suggestions, setSuggestions, analyzeText, rawAIResponse } = useAIAnalysis();
  const { dialogState, showConfirm, hideConfirm } = useConfirmDialog();

  // 组件挂载时恢复保存的建议
  useEffect(() => {
    const savedSuggestions = getWritingSuggestions();
    if (savedSuggestions.length > 0) {
      setSuggestions(savedSuggestions);
    }
  }, [setSuggestions]);
  const { applySuggestion, dismissSuggestion } = useSuggestionHandler(text, setText, suggestions, setSuggestions);
  const { isImmersiveMode, toggleImmersiveMode } = useImmersiveMode();

  // 处理文本变化
  const handleTextChange = (newText) => {
    setText(newText);
    // 自动保存文本
    autoSaveWritingText(newText, suggestions);
    
    if (newText.trim().length < 10) {
      setSuggestions([]);
    }
  };

  // 当建议变化时也要保存
  useEffect(() => {
    if (text) {
      autoSaveWritingText(text, suggestions);
    }
  }, [suggestions, text]);

  // 处理AI分析
  const handleAnalyze = async () => {
    if (!apiKey) {
      dispatch({ type: 'SHOW_API_CONFIG', payload: true });
      return;
    }
    try {
      await analyzeText(text, apiKey);
    } catch (error) {
      console.error('分析失败:', error);
      alert('分析失败，请检查API配置');
    }
  };

  // 处理选择历史记录
  const handleSelectHistory = (historyRecord) => {
    if (historyRecord) {
      setText(historyRecord.text);
      setSuggestions(historyRecord.suggestions || []);
    }
    closeModal();
  };

  // 处理查词请求
  const handleLookupWord = (word) => {
    console.log('EditorPage: 词典被请求，单词:', word);
    dispatch({ type: 'SHOW_DICTIONARY', payload: true, word: word });
  };

  // 处理新文档
  const handleNewDocument = () => {
    if (text.trim()) {
      showConfirm({
        title: '创建新文档',
        message: '确定要创建新文档吗？当前内容将被清除。',
        confirmText: '创建新文档',
        cancelText: '取消',
        type: 'warning',
        onConfirm: () => {
          setText('');
          setSuggestions([]);
          clearWritingText();
        }
      });
    } else {
      setText('');
      setSuggestions([]);
      clearWritingText();
    }
  };

  return (
    <div className="min-h-screen transition-colors duration-300" style={{
      backgroundColor: isDarkMode ? '#1A1611' : '#F5EFE6',
      height: isImmersiveMode ? '100vh' : 'auto',
      overflow: isImmersiveMode ? 'hidden' : 'auto',
      marginRight: isModalOpen('aiResponse') ? `calc(min(400px, 40vw) + 16px)` : '0',
      transition: 'margin-right 0.3s ease-in-out'
    }}>
      {!isImmersiveMode && (
        <Header
          onShowApiConfig={() => {
            console.log('EditorPage: 设置按钮被点击');
            dispatch({ type: 'SHOW_API_CONFIG', payload: true });
          }}
          onToggleImmersiveMode={toggleImmersiveMode}
          isDarkMode={isDarkMode}
        />
      )}

      <div className="max-w-7xl mx-auto" style={{
        padding: isImmersiveMode ? '80px 32px 32px 32px' : '32px 32px 48px 32px'
      }}>
        <VintageTextEditor
          text={text}
          setText={handleTextChange}
          suggestions={suggestions}
          isAnalyzing={isAnalyzing}
          onAnalyze={handleAnalyze}
          hoveredSuggestion={hoveredSuggestion}
          setHoveredSuggestion={setHoveredSuggestion}
          setBubblePosition={setBubblePosition}
          setActiveBubble={setActiveBubble}
          isImmersiveMode
          onShowAIResponse={() => openModal('aiResponse')}
          rawAIResponse={rawAIResponse}
          onShowHistory={() => openModal('history')}
          onShowDictionary={() => dispatch({ type: 'SHOW_DICTIONARY', payload: true })}
          onNewDocument={handleNewDocument}
          isDarkMode={isDarkMode}
        />
      </div>

      {activeBubble && (
        <SuggestionBubble
          suggestion={activeBubble}
          position={bubblePosition}
          onApply={applySuggestion}
          onDismiss={dismissSuggestion}
          onClose={() => setActiveBubble(null)}
          isDarkMode={isDarkMode}
        />
      )}

      <AIResponseSidebar
        isOpen={isModalOpen('aiResponse')}
        onClose={closeModal}
        content={rawAIResponse}
        isDarkMode={isDarkMode}
      />

      <HistoryModal
        isOpen={isModalOpen('history')}
        onClose={closeModal}
        onSelectHistory={handleSelectHistory}
        isDarkMode={isDarkMode}
      />

      <TextSelectionMenu onLookupWord={handleLookupWord} isDarkMode={isDarkMode} />

      {/* 边缘导航箭头 */}
      <EdgeNavigationArrow
        onSwitchToChat={() => {
          console.log('EdgeNavigationArrow: 切换到聊天模式');
          dispatch({ type: 'SET_CURRENT_PAGE', payload: 'chat' });
        }}
        isDarkMode={isDarkMode}
      />

      {/* 自定义确认对话框 */}
      <ConfirmDialog
        isOpen={dialogState.isOpen}
        onClose={hideConfirm}
        onConfirm={dialogState.onConfirm}
        title={dialogState.title}
        message={dialogState.message}
        confirmText={dialogState.confirmText}
        cancelText={dialogState.cancelText}
        type={dialogState.type}
        isDarkMode={isDarkMode}
      />
    </div>
  );
}
