import React from 'react';
import { X } from 'lucide-react';
import { useTheme } from '../ThemeProvider';
import ThemedButton from './ThemedButton';

const ThemedModal = ({ 
  isOpen, 
  onClose, 
  title, 
  children, 
  className = '',
  maxWidth = 'md',
  ...props 
}) => {
  const { isDarkMode } = useTheme();

  if (!isOpen) return null;

  // 最大宽度样式
  const maxWidthStyles = {
    sm: { maxWidth: '400px' },
    md: { maxWidth: '500px' },
    lg: { maxWidth: '600px' },
    xl: { maxWidth: '800px' },
  };

  // 背景遮罩样式
  const backdropStyles = {
    position: 'fixed',
    inset: '0',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: '50',
    backgroundColor: isDarkMode ? 'rgba(26, 22, 17, 0.6)' : 'rgba(93, 64, 55, 0.4)',
    transition: 'background-color 0.3s ease',
  };

  // 模态框内容样式
  const contentStyles = {
    backgroundColor: 'var(--color-bg-secondary)',
    color: 'var(--color-text-primary)',
    border: '1px solid var(--color-border)',
    borderRadius: '16px',
    width: '100%',
    margin: '0 32px',
    transition: 'background-color 0.3s ease, border-color 0.3s ease',
    ...maxWidthStyles[maxWidth],
  };

  // 头部样式
  const headerStyles = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '32px 32px 24px 32px',
  };

  // 标题样式
  const titleStyles = {
    fontSize: '20px',
    fontWeight: '600',
    color: 'var(--color-text-primary)',
    fontFamily: 'Georgia, "Noto Serif SC", serif',
    letterSpacing: '0.05em',
    margin: '0',
  };

  // 内容样式
  const bodyStyles = {
    padding: '0 32px 32px 32px',
  };

  // 处理背景点击
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      style={backdropStyles}
      onClick={handleBackdropClick}
      {...props}
    >
      <div
        className={`themed-modal ${className}`}
        style={contentStyles}
      >
        {/* 头部 */}
        <div style={headerStyles}>
          <h3 style={titleStyles}>{title}</h3>
          <ThemedButton
            variant="ghost"
            size="icon"
            onClick={onClose}
            style={{ width: '40px', height: '40px' }}
          >
            <X size={20} />
          </ThemedButton>
        </div>

        {/* 内容 */}
        <div style={bodyStyles}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default ThemedModal;