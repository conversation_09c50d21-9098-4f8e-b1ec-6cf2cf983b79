import React, { useState, useMemo, useEffect } from 'react';
import { Check, ChevronLeft, ChevronRight } from 'lucide-react';

const SuggestionBubble = ({
  suggestion,
  position,
  onApply,
  onDismiss,
  onClose,
  isDarkMode
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // 使用useEffect添加全局鼠标移动监听，提高悬浮效果的稳定性
  useEffect(() => {
    // 创建一个标志，表示鼠标是否在气泡内
    let isMouseInBubble = false;

    // 监听鼠标移动事件
    const handleMouseMove = (e) => {
      // 获取气泡元素
      const bubbleElement = document.querySelector('.suggestion-bubble');
      if (!bubbleElement) return;

      // 检查鼠标是否在气泡内
      const rect = bubbleElement.getBoundingClientRect();
      const isInside =
        e.clientX >= rect.left &&
        e.clientX <= rect.right &&
        e.clientY >= rect.top &&
        e.clientY <= rect.bottom;

      // 如果鼠标从外部移入气泡
      if (isInside && !isMouseInBubble) {
        isMouseInBubble = true;
        if (window.bubbleManager) {
          window.bubbleManager.clearTimeout();
        }
      }

      // 如果鼠标从气泡移出
      if (!isInside && isMouseInBubble) {
        isMouseInBubble = false;
        if (window.bubbleManager) {
          window.bubbleManager.setTimeout(() => {
            onClose();
          }, 800);
        }
      }
    };

    // 添加鼠标移动事件监听器
    document.addEventListener('mousemove', handleMouseMove);

    // 清理函数
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
    };
  }, [onClose]);

  // 处理Markdown格式的文本，渲染为React元素
  const formatText = (text) => {
    if (!text) return text;

    // 将文本按照不同的Markdown格式进行处理
    let processedText = text;
    const elements = [];
    let elementIndex = 0;

    // 处理 ## 标题格式
    processedText = processedText.replace(/^##\s+(.+)$/gm, (match, content) => {
      const placeholder = `__ELEMENT_${elementIndex}__`;
      elements[elementIndex] = (
        <h2
          key={`heading-${elementIndex}`}
          style={{
            fontSize: '1.25rem',
            fontWeight: '600',
            marginTop: '1rem',
            marginBottom: '0.75rem',
            color: '#8B4513',
            fontFamily: 'Georgia, "Noto Serif SC", serif'
          }}
        >
          {content}
        </h2>
      );
      elementIndex++;
      return placeholder;
    });

    // 处理 ### 子标题格式
    processedText = processedText.replace(/^###\s+(.+)$/gm, (match, content) => {
      const placeholder = `__ELEMENT_${elementIndex}__`;
      elements[elementIndex] = (
        <h3
          key={`subheading-${elementIndex}`}
          style={{
            fontSize: '1.1rem',
            fontWeight: '600',
            marginTop: '0.75rem',
            marginBottom: '0.5rem',
            color: '#8B4513',
            fontFamily: 'Georgia, "Noto Serif SC", serif'
          }}
        >
          {content}
        </h3>
      );
      elementIndex++;
      return placeholder;
    });

    // 预处理：移除空行和只包含特殊字符的行
    processedText = processedText
      .replace(/^\s*-\s*$/gm, '') // 移除只包含"-"的行
      .replace(/^\s*•\s*$/gm, '') // 移除只包含"•"的行
      .replace(/^\s*_+\s*$/gm, '') // 移除只包含下划线的行
      .replace(/^\s*<[^>]*>\s*$/gm, '') // 移除只包含HTML标签的行
      .replace(/^\s*\[\s*\]\s*$/gm, '') // 移除只包含[]的行
      .replace(/^\s*\(\s*\)\s*$/gm, '') // 移除只包含()的行
      .replace(/^\s*__ELEMENT_\d+__\s*$/gm, '') // 移除只包含占位符的行
      .replace(/^\s*$/gm, '') // 移除只包含空白的行
      .replace(/\n{2,}/g, '\n\n'); // 将2个以上的换行符替换为2个

    // 处理列表项 - 项目
    processedText = processedText.replace(/^-\s+(.+)$/gm, (match, content) => {
      // 跳过空内容或只包含特殊字符的内容
      if (!content.trim() || /^[\s_\-•<>[\]()]+$/.test(content.trim())) return '';

      const placeholder = `__ELEMENT_${elementIndex}__`;
      elements[elementIndex] = (
        <div
          key={`list-item-${elementIndex}`}
          style={{
            display: 'flex',
            marginBottom: '0.5rem',
            paddingLeft: '0.5rem'
          }}
        >
          <span style={{ marginRight: '0.5rem', color: '#8B4513' }}>•</span>
          <span>{content}</span>
        </div>
      );
      elementIndex++;
      return placeholder;
    });

    // 处理 **粗体** 格式
    processedText = processedText.replace(/\*\*([^*]+)\*\*/g, (match, content) => {
      const placeholder = `__ELEMENT_${elementIndex}__`;
      elements[elementIndex] = (
        <span
          key={`bold-${elementIndex}`}
          style={{
            backgroundColor: isDarkMode ? 'rgba(74, 63, 53, 0.4)' : 'rgba(240, 230, 210, 0.6)', // 与页面色调协调的背景
            color: isDarkMode ? '#E8DCC6' : '#5D4037', // 使用页面主色调
            padding: '3px 8px',
            borderRadius: '8px',
            fontWeight: '600',
            border: `1px solid ${isDarkMode ? 'rgba(74, 63, 53, 0.6)' : 'rgba(230, 215, 184, 0.8)'}`,
            backdropFilter: 'blur(5px)'
          }}
        >
          {content}
        </span>
      );
      elementIndex++;
      return placeholder;
    });

    // 处理 *斜体* 格式
    processedText = processedText.replace(/\*([^*]+)\*/g, (match, content) => {
      const placeholder = `__ELEMENT_${elementIndex}__`;
      elements[elementIndex] = (
        <span
          key={`italic-${elementIndex}`}
          style={{
            fontStyle: 'italic',
            color: isDarkMode ? '#E8DCC6' : '#5D4037'
          }}
        >
          {content}
        </span>
      );
      elementIndex++;
      return placeholder;
    });

    // 处理引号内容 "text" 或 "text"
    processedText = processedText.replace(/[""]([^"""]+)[""]|"([^"]+)"/g, (match, content1, content2) => {
      const content = content1 || content2;
      const placeholder = `__ELEMENT_${elementIndex}__`;
      elements[elementIndex] = (
        <span
          key={`quote-${elementIndex}`}
          style={{
            backgroundColor: isDarkMode ? 'rgba(210, 105, 30, 0.2)' : 'rgba(240, 230, 210, 0.8)', // 使用页面橙色调
            color: isDarkMode ? '#D2691E' : '#8B4513', // 使用页面主色调
            padding: '3px 8px',
            borderRadius: '8px',
            fontStyle: 'italic',
            border: `1px solid ${isDarkMode ? 'rgba(210, 105, 30, 0.4)' : 'rgba(230, 215, 184, 0.9)'}`,
            backdropFilter: 'blur(5px)'
          }}
        >
          {content}
        </span>
      );
      elementIndex++;
      return placeholder;
    });

    // 处理段落分隔
    processedText = processedText.replace(/\n\s*\n/g, (match) => {
      const placeholder = `__ELEMENT_${elementIndex}__`;
      elements[elementIndex] = (
        <div
          key={`paragraph-break-${elementIndex}`}
          style={{
            height: '0.75rem'
          }}
        />
      );
      elementIndex++;
      return placeholder;
    });

    // 将处理后的文本分割并插入React元素
    const parts = [];
    let lastIndex = 0;

    // 查找所有占位符
    const placeholderRegex = /__ELEMENT_(\d+)__/g;
    let match;

    while ((match = placeholderRegex.exec(processedText)) !== null) {
      // 添加前面的普通文本
      if (match.index > lastIndex) {
        const textSegment = processedText.substring(lastIndex, match.index);
        // 处理换行符
        const lines = textSegment.split('\n');
        lines.forEach((line, i) => {
          if (i > 0) parts.push(<br key={`br-${lastIndex}-${i}`} />);
          if (line) parts.push(line);
        });
      }

      // 添加对应的React元素
      const elementIdx = parseInt(match[1]);
      parts.push(elements[elementIdx]);

      lastIndex = match.index + match[0].length;
    }

    // 添加剩余的文本
    if (lastIndex < processedText.length) {
      const textSegment = processedText.substring(lastIndex);
      // 处理换行符
      const lines = textSegment.split('\n');
      lines.forEach((line, i) => {
        if (i > 0) parts.push(<br key={`br-end-${i}`} />);
        if (line) parts.push(line);
      });
    }

    return parts.length > 0 ? parts : text;
  };

  // 解析AI分析内容，将其分解为多个部分
  const analysisSegments = useMemo(() => {
    if (!suggestion.detailedAnalysis && !suggestion.explanation) {
      return [{
        title: suggestion.suggestion || '建议',
        content: suggestion.explanation || '暂无详细说明'
      }];
    }

    // 使用当前建议的原始文本作为标题
    const title = suggestion.original || '建议';

    // 使用当前建议的详细分析或解释作为内容
    let fullText = suggestion.detailedAnalysis || suggestion.explanation;

    // 清理文本中的空行和多余空格
    fullText = fullText
      // 移除空行和只包含特殊字符的行
      .replace(/^\s*-\s*$/gm, '') // 移除只包含"-"的行
      .replace(/^\s*•\s*$/gm, '') // 移除只包含"•"的行
      .replace(/^\s*_+\s*$/gm, '') // 移除只包含下划线的行
      .replace(/^\s*<[^>]*>\s*$/gm, '') // 移除只包含HTML标签的行
      .replace(/^\s*\[\s*\]\s*$/gm, '') // 移除只包含[]的行
      .replace(/^\s*\(\s*\)\s*$/gm, '') // 移除只包含()的行
      .replace(/^\s*__ELEMENT_\d+__\s*$/gm, '') // 移除只包含占位符的行
      .replace(/_ELEMENT_\d+_/g, '') // 移除特殊占位符
      .replace(/^\s*$/gm, '') // 移除只包含空白的行
      // 处理特殊情况
      .replace(/\n+\s*•\s*\n+/g, '\n') // 移除单独的项目符号
      .replace(/\n+\s*-\s*\n+/g, '\n') // 移除单独的连字符
      .replace(/\n{2,}/g, '\n\n') // 将2个以上的换行符替换为2个
      .trim();

    // 如果有具体的替换建议，创建一个专门的段落
    if (suggestion.replacement) {
      return [{
        title: `语法与表达问题`,
        content: fullText // 移除重复的原文和建议修改信息
      }];
    }

    // 如果没有具体的替换建议，但有分析内容
    return [{
      title: `分析建议`,
      content: fullText
    }];
  }, [suggestion]);

  return (
    <>
      {/* 气泡内容 */}
      <div
        className="fixed z-50 max-w-sm shadow-xl suggestion-bubble custom-scrollbar"
        style={{
          backgroundColor: isDarkMode ? '#2A241D' : '#FEFCF5',
          left: `${position.x}px`,
          top: `${position.y}px`,
          border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`,
          transform: 'translateY(-100%)', // 让气泡显示在指定位置的上方
          zIndex: 1000, // 确保卡片在最上层
          maxHeight: '280px', // 限制最大高度
          overflowY: 'auto', // 如果内容过多，允许滚动
          borderRadius: '20px', // 更大的圆角，符合页面风格
          transition: 'all 0.3s ease',
          // 添加温暖的阴影效果
          boxShadow: isDarkMode 
            ? '0 20px 60px rgba(0, 0, 0, 0.4), 0 8px 24px rgba(0, 0, 0, 0.3)' 
            : '0 20px 60px rgba(93, 64, 55, 0.15), 0 8px 24px rgba(93, 64, 55, 0.1)',
          backdropFilter: 'blur(10px)',
          // 自定义滚动条样式
          scrollbarWidth: 'thin',
          scrollbarColor: isDarkMode ? 'rgba(74, 63, 53, 0.5) transparent' : 'rgba(230, 215, 184, 0.5) transparent',
          msOverflowStyle: 'none', // IE and Edge
        }}
        onMouseEnter={() => {
          // 当鼠标进入气泡时，使用全局bubbleManager清除超时
          if (window.bubbleManager) {
            window.bubbleManager.clearTimeout();
          }

          // 发布一个自定义事件，通知其他组件鼠标已进入气泡
          const event = new CustomEvent('bubbleMouseEnter', {
            detail: { suggestionId: suggestion.id }
          });
          document.dispatchEvent(event);
        }}
        onMouseLeave={() => {
          // 当鼠标离开气泡时，使用全局bubbleManager设置延迟关闭
          if (window.bubbleManager) {
            window.bubbleManager.setTimeout(() => {
              onClose();
            }, 800); // 使用与VintageTextEditor相同的延迟时间
          } else {
            // 如果全局bubbleManager不可用，使用普通的setTimeout
            setTimeout(() => {
              onClose();
            }, 800);
          }
        }}
      >

        <div style={{ padding: '16px', display: 'flex', flexDirection: 'column' }}>
          {/* 分段内容显示 - 纯文本解释区域 */}
          <div
            className="relative p-4 rounded-xl mb-2"
            style={{
              backgroundColor: 'transparent',
              border: 'none'
            }}
          >

            {/* 导航（如果有多个段落） */}
            {analysisSegments.length > 1 && (
              <div className="flex items-center justify-end mb-3">
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setCurrentIndex(Math.max(0, currentIndex - 1))}
                    disabled={currentIndex === 0}
                    className="nav-btn"
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </button>

                  <span className="text-xs px-3 py-1 rounded-lg" style={{
                    color: isDarkMode ? '#E8DCC6' : '#5D4037',
                    backgroundColor: isDarkMode ? 'rgba(74, 63, 53, 0.4)' : 'rgba(240, 230, 210, 0.6)',
                    fontFamily: 'Georgia, "Noto Serif SC", serif',
                    letterSpacing: '0.05em',
                    border: `1px solid ${isDarkMode ? 'rgba(74, 63, 53, 0.6)' : 'rgba(230, 215, 184, 0.8)'}`,
                    backdropFilter: 'blur(5px)'
                  }}>
                    {currentIndex + 1} / {analysisSegments.length}
                  </span>

                  <button
                    onClick={() => setCurrentIndex(Math.min(analysisSegments.length - 1, currentIndex + 1))}
                    disabled={currentIndex === analysisSegments.length - 1}
                    className="nav-btn"
                  >
                    <ChevronRight className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}

            {/* 当前段落内容 */}
            <div className="text-sm leading-relaxed transition-colors duration-300" style={{
              color: isDarkMode ? '#E8DCC6' : '#5D4037',
              fontFamily: 'Georgia, "Noto Serif SC", serif',
              letterSpacing: '0.05em',
              whiteSpace: 'pre-wrap',
              minHeight: '60px'
            }}>
              {formatText(analysisSegments[currentIndex]?.content)}
            </div>
          </div>

          {/* 如果有具体的建议对比，显示建议修改 - 点击此区域采纳建议 */}
          {suggestion.original && suggestion.replacement && (
            <div className="mt-2">
              <div
                className="suggestion-replacement-btn"
                onClick={() => {
                  onApply(suggestion);
                  onClose();
                }}
              >
                建议修改为：{suggestion.replacement}
              </div>
            </div>
          )}

          {/* 如果没有replacement但有explanation，显示说明 */}
          {!suggestion.replacement && suggestion.explanation && !suggestion.detailedAnalysis && (
            <div className="p-4 rounded-xl text-sm mt-2 transition-all duration-300" style={{
              backgroundColor: isDarkMode ? 'rgba(74, 63, 53, 0.4)' : 'rgba(240, 230, 210, 0.6)',
              color: isDarkMode ? '#E8DCC6' : '#5D4037',
              fontFamily: 'Georgia, "Noto Serif SC", serif',
              letterSpacing: '0.05em',
              border: `1px solid ${isDarkMode ? 'rgba(74, 63, 53, 0.6)' : 'rgba(230, 215, 184, 0.8)'}`,
              backdropFilter: 'blur(5px)'
            }}>
              {suggestion.explanation}
            </div>
          )}
        </div>


      </div>
    </>
  );
};

export default SuggestionBubble;
