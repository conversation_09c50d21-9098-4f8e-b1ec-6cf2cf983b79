import React, { useState, useEffect } from 'react';
import { X, Sun, Moon, Volume2 } from 'lucide-react';

const VintageApiConfigModal = ({ isOpen, onClose, onSave, isDarkMode, onToggleDarkMode, autoPlayTTS, onToggleAutoPlayTTS }) => {
  const [apiKey, setApiKey] = useState('');

  useEffect(() => {
    if (isOpen) {
      const currentKey = localStorage.getItem('doubao_api_key') || '';
      setApiKey(currentKey);
    }
  }, [isOpen]);

  const handleSave = () => {
    localStorage.setItem('doubao_api_key', apiKey);
    onSave(apiKey);
    onClose();
  };

  const handleBackdropClick = (e) => {
    // 只有点击遮罩层本身时才关闭，点击弹窗内容不关闭
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 transition-colors duration-300"
      style={{ backgroundColor: isDarkMode ? 'rgba(26, 22, 17, 0.6)' : 'rgba(93, 64, 55, 0.4)' }}
      onClick={handleBackdropClick}
    >
      <div className="rounded-2xl max-w-md w-full mx-8 transition-colors duration-300" style={{
        backgroundColor: isDarkMode ? '#332B22' : '#FEFCF5'
      }}>
        <div className="flex items-center justify-between" style={{
          padding: '32px 32px 24px 32px'
        }}>
          <h3 className="text-xl font-semibold transition-colors duration-300" style={{
            color: isDarkMode ? '#E8DCC6' : '#5D4037',
            fontFamily: 'Georgia, "Noto Serif SC", serif',
            letterSpacing: '0.05em'
          }}>设置</h3>
          <button
            onClick={onClose}
            className="header-btn"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div style={{ padding: '0 32px 32px 32px' }}>
          {/* API Key 配置 */}
          <div style={{ marginBottom: '32px' }}>
            <label className="block text-sm font-medium transition-colors duration-300" style={{
              color: isDarkMode ? '#E8DCC6' : '#5D4037',
              fontFamily: 'Georgia, "Noto Serif SC", serif',
              letterSpacing: '0.05em',
              marginBottom: '12px'
            }}>
              豆包 API Key
            </label>
            <input
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="请输入你的豆包 API Key"
              className="w-full rounded-xl focus:outline-none transition-all duration-200"
              style={{
                backgroundColor: isDarkMode ? '#2A241D' : '#FFFEF7',
                color: isDarkMode ? '#E8DCC6' : '#5D4037',
                fontFamily: 'Georgia, "Noto Serif SC", serif',
                padding: '16px 20px',
                fontSize: '16px',
                border: `1px solid ${isDarkMode ? '#4A3F35' : 'transparent'}`
              }}
              onFocus={(e) => {
                e.target.style.backgroundColor = isDarkMode ? '#332B22' : '#FFFDF0';
              }}
              onBlur={(e) => {
                e.target.style.backgroundColor = isDarkMode ? '#2A241D' : '#FFFEF7';
              }}
            />
          </div>

          {/* 设置选项 */}
          <div style={{ marginBottom: '32px' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              

              {/* 主题切换 */}
              <div 
                onClick={onToggleDarkMode}
                className="settings-option-btn"
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  {isDarkMode ? (
                    <Moon className="w-5 h-5" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                  ) : (
                    <Sun className="w-5 h-5" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                  )}
                  <div>
                    <div style={{
                      color: isDarkMode ? '#E8DCC6' : '#5D4037',
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      fontSize: '16px',
                      fontWeight: '500'
                    }}>
                      {isDarkMode ? '暗色模式' : '浅色模式'}
                    </div>
                    <div style={{
                      color: isDarkMode ? '#C4B59A' : '#8B4513',
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      fontSize: '14px',
                      marginTop: '2px'
                    }}>
                      {isDarkMode ? '切换到浅色主题' : '切换到暗色主题'}
                    </div>
                  </div>
                </div>
              </div>

              {/* 语音自动播放开关 */}
              <div 
                onClick={onToggleAutoPlayTTS}
                className="settings-option-btn"
                style={{ justifyContent: 'space-between' }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <Volume2 className="w-5 h-5" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                  <div>
                    <div style={{
                      color: isDarkMode ? '#E8DCC6' : '#5D4037',
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      fontSize: '16px',
                      fontWeight: '500'
                    }}>
                      语音自动播放
                    </div>
                    <div style={{
                      color: isDarkMode ? '#C4B59A' : '#8B4513',
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      fontSize: '14px',
                      marginTop: '2px'
                    }}>
                      {autoPlayTTS ? 'AI回复时自动播放语音' : '点击播放按钮播放语音'}
                    </div>
                  </div>
                </div>
                <div style={{
                  width: '48px',
                  height: '24px',
                  borderRadius: '12px',
                  backgroundColor: autoPlayTTS 
                    ? (isDarkMode ? '#D2691E' : '#166534')
                    : (isDarkMode ? '#4A3F35' : '#D4C4A8'),
                  position: 'relative',
                  transition: 'all 0.2s ease',
                  cursor: 'pointer',
                  flexShrink: 0
                }}>
                  <div style={{
                    width: '20px',
                    height: '20px',
                    borderRadius: '10px',
                    backgroundColor: '#FEFCF5',
                    position: 'absolute',
                    top: '2px',
                    left: autoPlayTTS ? '26px' : '2px',
                    transition: 'all 0.2s ease',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
                  }} />
                </div>
              </div>
            </div>
          </div>

          {/* 保存按钮 */}
          <div>
            <button
              onClick={handleSave}
              className="save-settings-btn"
            >
              保存设置
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VintageApiConfigModal;
