
import React, { createContext, useReducer, useContext, useEffect } from 'react';
import { useModalManager } from '../hooks/useModalManager';

// 1. 定义初始状态
const initialState = {
  currentPage: 'editor', // 'editor' 或 'chat'
  isDarkMode: localStorage.getItem('theme') === 'dark',
  lookupWord: '',
  autoPlayTTS: localStorage.getItem('auto_play_tts') === 'true',
  aiResponseSound: localStorage.getItem('ai_response_sound') === 'true',
  showApiConfig: false,
  showDictionary: false,
};

// 2. 创建 Reducer 函数
function appReducer(state, action) {
  switch (action.type) {
    case 'SET_CURRENT_PAGE':
      return { ...state, currentPage: action.payload };
    case 'TOGGLE_DARK_MODE':
      localStorage.setItem('theme', !state.isDarkMode ? 'dark' : 'light');
      return { ...state, isDarkMode: !state.isDarkMode };
    case 'SET_LOOKUP_WORD':
      return { ...state, lookupWord: action.payload };
    case 'TOGGLE_AUTO_PLAY_TTS':
      localStorage.setItem('auto_play_tts', (!state.autoPlayTTS).toString());
      return { ...state, autoPlayTTS: !state.autoPlayTTS };
    case 'TOGGLE_AI_RESPONSE_SOUND':
      localStorage.setItem('ai_response_sound', (!state.aiResponseSound).toString());
      return { ...state, aiResponseSound: !state.aiResponseSound };
    case 'SHOW_API_CONFIG':
      return { ...state, showApiConfig: action.payload };
    case 'SHOW_DICTIONARY':
      return { ...state, showDictionary: action.payload, lookupWord: action.word || state.lookupWord };
    default:
      return state;
  }
}

// 3. 创建 Context
const AppContext = createContext();

// 4. 创建 Context Provider 组件
export function AppProvider({ children }) {
  const [state, dispatch] = useReducer(appReducer, initialState);
  const { activeModal, openModal, closeModal, isModalOpen } = useModalManager();

  // 当 isDarkMode 状态改变时，应用到DOM并保存到localStorage
  useEffect(() => {
    const root = document.documentElement;
    if (state.isDarkMode) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
    localStorage.setItem('theme', state.isDarkMode ? 'dark' : 'light');
  }, [state.isDarkMode]);

  const contextValue = {
    state,
    dispatch,
    activeModal,
    openModal,
    closeModal,
    isModalOpen,
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
}

// 5. 创建自定义 Hook 以方便使用
export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}
