import React from 'react';
import { X } from 'lucide-react';
import ReactMarkdown from 'react-markdown';

const AIResponseModal = ({
  isOpen,
  onClose,
  content,
  isDarkMode
}) => {
  if (!isOpen) return null;

  // 预处理内容，移除"分析与改进建议"标题
  const preprocessContent = (content) => {
    if (!content) return '';

    // 移除"分析与改进建议"标题行
    return content.replace(/^分析与改进建议\s*$/m, '').trim();
  };

  // 使用预处理后的内容
  const processedContent = preprocessContent(content);

  // 自定义渲染器，为特定标题添加样式
  const components = {
    // 处理标题
    h1: ({ node, ...props }) => (
      <h1
        style={{
          color: 'var(--color-text-secondary)',
          fontWeight: 'bold',
          fontSize: '1.5em',
          marginTop: '1em',
          marginBottom: '0.5em'
        }}
        {...props}
      />
    ),
    h2: ({ node, ...props }) => (
      <h2
        style={{
          color: 'var(--color-text-secondary)',
          fontWeight: 'bold',
          fontSize: '1.3em',
          marginTop: '1em',
          marginBottom: '0.5em',
          backgroundColor: 'var(--color-bg-accent)',
          padding: '4px 12px',
          borderRadius: '4px',
          display: 'inline-block'
        }}
        {...props}
      />
    ),
    h3: ({ node, ...props }) => (
      <h3
        style={{
          color: 'var(--color-text-secondary)',
          fontWeight: 'bold',
          fontSize: '1.1em',
          marginTop: '1em',
          marginBottom: '0.5em',
          backgroundColor: 'var(--color-bg-accent)',
          padding: '4px 12px',
          borderRadius: '4px',
          display: 'inline-block'
        }}
        {...props}
      />
    ),
    // 处理段落
    p: ({ node, children, ...props }) => {
      // 检查是否是标题段落（如"拼写与标点问题"、"语法与表达问题"等）
      const text = children.toString();
      const isHeadingParagraph =
        text.includes('拼写与标点问题') ||
        text.includes('语法与表达问题') ||
        text.includes('风格优化');

      // 跳过"分析与改进建议"标题
      if (text.trim() === '分析与改进建议') {
        return null;
      }

      if (isHeadingParagraph) {
        return (
          <p
            style={{
              color: 'var(--color-text-secondary)',
              fontWeight: 'bold',
              fontSize: '1.2em',
              marginTop: '1em',
              marginBottom: '0.5em',
              backgroundColor: 'var(--color-bg-accent)',
              padding: '4px 12px',
              borderRadius: '4px',
              display: 'inline-block'
            }}
            {...props}
          >
            {children}
          </p>
        );
      }

      return <p style={{ marginTop: '0.5em', marginBottom: '0.5em' }} {...props}>{children}</p>;
    },
    // 处理列表项
    li: ({ node, ...props }) => (
      <li
        style={{
          marginTop: '0.25em',
          marginBottom: '0.25em'
        }}
        {...props}
      />
    )
  };

  return (
    <>
      <div
        className="fixed inset-0 z-40 transition-colors duration-300"
        style={{ backgroundColor: 'var(--modal-backdrop)' }}
        onClick={onClose}
      />
      <div className="fixed z-50 flex flex-col transition-colors duration-300" style={{
        backgroundColor: 'var(--color-bg-secondary)',
        top: '10%',
        left: '15%',
        right: '15%',
        bottom: '10%',
        maxHeight: '80vh',
        maxWidth: '800px',
        margin: '0 auto',
        width: '70%',
        borderRadius: '16px', // 使用固定的圆角值，确保所有角落都是圆角
        overflow: 'hidden' // 确保内容不会溢出圆角
      }}>
        <div className="flex items-center justify-between" style={{
          padding: '24px 24px 16px 24px'
        }}>
          <h3 className="text-xl font-semibold transition-colors duration-300" style={{
            color: 'var(--color-text-primary)',
            fontFamily: 'Georgia, "Noto Serif SC", serif'
          }}>分析与改进建议</h3>
          <button
            onClick={onClose}
            className="header-btn focus:outline-none"
          >
            <X className="w-6 h-6" />
          </button>
        </div>
        <div className="flex-1 overflow-y-auto" style={{
          padding: '0 24px 24px 24px',
          maxWidth: '100%',
          overflowX: 'hidden'
        }}>
          <div className="prose prose-sm max-w-none transition-colors duration-300" style={{
            width: '100%',
            color: 'var(--color-text-primary)',
            fontFamily: 'Georgia, "Noto Serif SC", serif'
          }}>
            <ReactMarkdown components={components}>{processedContent}</ReactMarkdown>
          </div>
        </div>
      </div>
    </>
  );
};

export default AIResponseModal;
