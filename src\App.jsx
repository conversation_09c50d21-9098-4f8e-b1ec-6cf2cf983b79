
import React from 'react';
import { AppProvider, useAppContext } from './context/AppContext';
import EditorPage from './pages/EditorPage';
import ChatPage from './pages/ChatPage';
import VintageApiConfigModal from './components/VintageApiConfigModal';
import DictionaryModal from './components/DictionaryModal';

function App() {
  return (
    <AppProvider>
      <MainContent />
    </AppProvider>
  );
}

function MainContent() {
  const { state, dispatch } = useAppContext();
  const { currentPage, isDarkMode, showApiConfig, showDictionary, lookupWord, autoPlayTTS } = state;

  const handleSaveApiKey = (newApiKey) => {
    localStorage.setItem('doubao_api_key', newApiKey);
  };

  return (
    <>
      {currentPage === 'editor' ? (
        <EditorPage />
      ) : (
        <ChatPage
          onBackToEditor={() => dispatch({ type: 'SET_CURRENT_PAGE', payload: 'editor' })}
          onShowApiConfig={() => dispatch({ type: 'SHOW_API_CONFIG', payload: true })}
          isDarkMode={isDarkMode}
          autoPlayTTS={autoPlayTTS}
        />
      )}

      <VintageApiConfigModal
        isOpen={showApiConfig}
        onClose={() => dispatch({ type: 'SHOW_API_CONFIG', payload: false })}
        onSave={handleSaveApiKey}
        isDarkMode={isDarkMode}
        onToggleDarkMode={() => dispatch({ type: 'TOGGLE_DARK_MODE' })}
        autoPlayTTS={autoPlayTTS}
        onToggleAutoPlayTTS={() => dispatch({ type: 'TOGGLE_AUTO_PLAY_TTS' })}
      />

      <DictionaryModal
        isOpen={showDictionary}
        onClose={() => dispatch({ type: 'SHOW_DICTIONARY', payload: false })}
        word={lookupWord}
        isDarkMode={isDarkMode}
      />
    </>
  );
}

export default App;
