import { useState, useRef, useCallback } from 'react';

export function useSpeechRecognition() {
    const [isListening, setIsListening] = useState(false);
    const [transcript, setTranscript] = useState('');
    const [error, setError] = useState(null);
    const [isSupported, setIsSupported] = useState(false);
    
    const recognitionRef = useRef(null);
    const finalTranscriptRef = useRef('');

    // 检查浏览器支持
    const checkSupport = useCallback(() => {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const supported = !!SpeechRecognition;
        setIsSupported(supported);
        return supported;
    }, []);

    // 初始化语音识别
    const initializeRecognition = useCallback(() => {
        if (!checkSupport()) {
            setError('您的浏览器不支持语音识别功能');
            return null;
        }

        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();

        // 配置语音识别
        recognition.continuous = true; // 持续监听
        recognition.interimResults = true; // 显示临时结果
        recognition.lang = 'en-US'; // 设置为英语
        recognition.maxAlternatives = 1;

        // 识别结果处理
        recognition.onresult = (event) => {
            let interimTranscript = '';
            let finalTranscript = finalTranscriptRef.current;

            for (let i = event.resultIndex; i < event.results.length; i++) {
                const result = event.results[i];
                if (result.isFinal) {
                    finalTranscript += result[0].transcript;
                } else {
                    interimTranscript += result[0].transcript;
                }
            }

            finalTranscriptRef.current = finalTranscript;
            setTranscript(finalTranscript + interimTranscript);
        };

        // 错误处理
        recognition.onerror = (event) => {
            console.error('语音识别错误:', event.error);
            setIsListening(false);
            
            switch (event.error) {
                case 'no-speech':
                    setError('没有检测到语音，请重试');
                    break;
                case 'audio-capture':
                    setError('无法访问麦克风，请检查权限设置');
                    break;
                case 'not-allowed':
                    setError('麦克风权限被拒绝，请在浏览器设置中允许麦克风访问');
                    break;
                case 'network':
                    setError('网络错误，请检查网络连接');
                    break;
                default:
                    setError(`语音识别出错: ${event.error}`);
            }
        };

        // 识别开始
        recognition.onstart = () => {
            console.log('语音识别开始');
            setIsListening(true);
            setError(null);
        };

        // 识别结束
        recognition.onend = () => {
            console.log('语音识别结束');
            setIsListening(false);
        };

        return recognition;
    }, [checkSupport]);

    // 开始语音识别
    const startListening = useCallback(() => {
        if (!recognitionRef.current) {
            recognitionRef.current = initializeRecognition();
        }

        if (recognitionRef.current && !isListening) {
            try {
                finalTranscriptRef.current = '';
                setTranscript('');
                setError(null);
                recognitionRef.current.start();
            } catch (error) {
                console.error('启动语音识别失败:', error);
                setError('启动语音识别失败，请重试');
            }
        }
    }, [initializeRecognition, isListening]);

    // 停止语音识别
    const stopListening = useCallback(() => {
        if (recognitionRef.current && isListening) {
            recognitionRef.current.stop();
        }
    }, [isListening]);

    // 清除转录文本
    const clearTranscript = useCallback(() => {
        setTranscript('');
        finalTranscriptRef.current = '';
    }, []);

    // 获取最终的转录文本
    const getFinalTranscript = useCallback(() => {
        return finalTranscriptRef.current.trim();
    }, []);

    return {
        isListening,
        transcript,
        error,
        isSupported,
        startListening,
        stopListening,
        clearTranscript,
        getFinalTranscript,
        checkSupport
    };
}