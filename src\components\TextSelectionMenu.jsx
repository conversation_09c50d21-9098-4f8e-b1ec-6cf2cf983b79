import React, { useState, useEffect, useRef } from 'react';
import { Book, Copy, Volume2 } from 'lucide-react';

const TextSelectionMenu = ({ onLookupWord, isDarkMode }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [selectedText, setSelectedText] = useState('');
  const menuRef = useRef(null);

  // 监听文本选择事件
  useEffect(() => {
    const handleSelectionChange = () => {
      const selection = window.getSelection();
      
      if (selection.toString().trim()) {
        // 有文本被选中
        setSelectedText(selection.toString().trim());
        
        // 获取选中文本的位置
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();
        
        // 设置菜单位置（在选中文本上方）
        setPosition({
          x: rect.left + rect.width / 2,
          y: rect.top - 10
        });
        
        setIsVisible(true);
      } else {
        // 没有文本被选中
        setIsVisible(false);
      }
    };

    // 点击其他区域时隐藏菜单
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsVisible(false);
      }
    };

    document.addEventListener('selectionchange', handleSelectionChange);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理查词
  const handleLookup = () => {
    if (selectedText) {
      onLookupWord(selectedText);
      setIsVisible(false);
    }
  };

  // 处理复制
  const handleCopy = () => {
    if (selectedText) {
      navigator.clipboard.writeText(selectedText)
        .then(() => {
          console.log('文本已复制到剪贴板');
          setIsVisible(false);
        })
        .catch(err => {
          console.error('复制失败:', err);
        });
    }
  };

  // 处理朗读
  const handleSpeak = () => {
    if (selectedText && window.speechSynthesis) {
      const utterance = new SpeechSynthesisUtterance(selectedText);
      utterance.lang = 'en-US';
      window.speechSynthesis.speak(utterance);
    }
  };

  if (!isVisible) return null;

  return (
    <div
      ref={menuRef}
      className="fixed z-50 flex items-center gap-1 rounded-lg shadow-lg"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: 'translate(-50%, -100%)',
        backgroundColor: 'var(--color-bg-secondary)',
        border: '1px solid var(--color-border)',
        padding: '6px',
        transition: 'background-color 0.3s ease, border-color 0.3s ease'
      }}
    >
      <button
        onClick={handleLookup}
        className="selection-menu-btn"
        style={{ 
          color: 'var(--color-accent-green)'
        }}
        title="查询词典"
      >
        <Book className="w-4 h-4" />
      </button>
      
      <button
        onClick={handleCopy}
        className="selection-menu-btn"
        style={{ 
          color: 'var(--color-text-secondary)'
        }}
        title="复制文本"
      >
        <Copy className="w-4 h-4" />
      </button>
      
      <button
        onClick={handleSpeak}
        className="selection-menu-btn"
        style={{ 
          color: 'var(--color-accent-red)'
        }}
        title="朗读文本"
      >
        <Volume2 className="w-4 h-4" />
      </button>
    </div>
  );
};

export default TextSelectionMenu;